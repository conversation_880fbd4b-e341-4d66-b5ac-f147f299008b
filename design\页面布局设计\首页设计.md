# 首页设计规范

## 📱 页面概述

首页是用户进入小程序的第一入口，需要清晰展示四大核心功能，提供便捷的扫码服务，并展示最新的本地信息。

## 🎨 整体布局

### 页面结构
```
首页 (750rpx × 1334rpx)
├── 状态栏 (44rpx)
├── 搜索头部 (120rpx)
├── 扫码服务卡片 (140rpx)
├── 四大板块导航 (280rpx)
├── 快速发布区域 (160rpx)
├── 信息流区域 (可滚动)
└── Tab栏 (98rpx)
```

## 🔍 搜索头部设计

### 布局规范
- **总高度**: 120rpx (60px)
- **背景色**: 主色渐变 (#07C160)
- **内边距**: 20rpx 30rpx
- **元素间距**: 20rpx

### 搜索栏设计
```css
.search-bar {
  flex: 1;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 40rpx;
  padding: 0 24rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  color: #C5CAD0;
}

.search-placeholder {
  font-size: 28rpx;
  color: #C5CAD0;
}
```

### 扫码按钮设计
```css
.scan-btn {
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.scan-icon {
  width: 40rpx;
  height: 40rpx;
  color: white;
}
```

## 📷 扫码服务卡片

### 设计规范
- **高度**: 140rpx (70px)
- **边距**: 30rpx (左右), 20rpx (上下)
- **圆角**: 20rpx
- **背景**: 紫色渐变 (#667eea → #764ba2)
- **阴影**: 0 4rpx 20rpx rgba(102, 126, 234, 0.3)

### 布局结构
```css
.scan-service-card {
  display: flex;
  align-items: center;
  padding: 30rpx;
  gap: 24rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(102, 126, 234, 0.3);
}

.scan-icon-wrapper {
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.scan-content {
  flex: 1;
  color: white;
}

.scan-title {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.scan-desc {
  font-size: 24rpx;
  opacity: 0.8;
}
```

## 🧭 四大板块导航

### 网格布局
- **布局**: 2×2 网格
- **间距**: 20rpx
- **卡片高度**: 120rpx
- **总区域高度**: 260rpx

### 商铺板块卡片
```css
.nav-shops {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16rpx;
  padding: 30rpx 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.2);
}

.nav-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 16rpx;
  filter: brightness(0) invert(1); /* 白色图标 */
}

.nav-title {
  font-size: 28rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 4rpx;
}

.nav-desc {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.8);
}
```

### 信息板块卡片
```css
.nav-info {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  /* 其他样式同商铺板块 */
}
```

### 出行板块卡片
```css
.nav-transport {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  /* 其他样式同商铺板块 */
}
```

### 跑腿板块卡片
```css
.nav-errands {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  /* 其他样式同商铺板块 */
}
```

## ⚡ 快速发布区域

### 区域设计
- **标题**: "快速发布" (32rpx, 600字重)
- **布局**: 4个发布按钮横向排列
- **按钮尺寸**: 140rpx × 100rpx
- **间距**: 16rpx

### 发布按钮设计
```css
.publish-item {
  width: 140rpx;
  height: 100rpx;
  background: white;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  gap: 8rpx;
}

.publish-icon {
  width: 32rpx;
  height: 32rpx;
  color: #07C160;
}

.publish-text {
  font-size: 22rpx;
  color: #191F25;
  font-weight: 500;
}
```

## 📰 信息流区域

### 区域头部
```css
.feed-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #191F25;
}

.section-more {
  font-size: 24rpx;
  color: #8B9197;
}
```

### 信息卡片设计
```css
.feed-item {
  background: white;
  border-radius: 12rpx;
  padding: 24rpx;
  margin: 0 30rpx 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.feed-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.feed-type-tag {
  padding: 6rpx 12rpx;
  border-radius: 6rpx;
  font-size: 20rpx;
  font-weight: 500;
  color: white;
}

.feed-type-shops {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.feed-type-info {
  background: linear-gradient(135deg, #f093fb, #f5576c);
}

.feed-type-transport {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.feed-type-errands {
  background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.feed-time {
  font-size: 22rpx;
  color: #8B9197;
}

.feed-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #191F25;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.feed-content {
  font-size: 24rpx;
  color: #8B9197;
  line-height: 1.5;
  margin-bottom: 16rpx;
}

.feed-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.feed-location {
  display: flex;
  align-items: center;
  gap: 6rpx;
  font-size: 22rpx;
  color: #8B9197;
}

.feed-contact {
  display: flex;
  align-items: center;
  gap: 6rpx;
  font-size: 22rpx;
  color: #07C160;
  font-weight: 500;
}
```

## 🎯 交互状态

### 点击反馈
```css
.clickable:active {
  transform: scale(0.98);
  transition: transform 0.1s ease;
}

.card:active {
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.15);
}
```

### 加载状态
```css
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}
```

## 📱 响应式适配

### 小屏适配 (iPhone SE)
```css
@media (max-width: 320px) {
  .nav-grid {
    gap: 12rpx;
  }
  
  .nav-item {
    padding: 20rpx 16rpx;
  }
  
  .publish-item {
    width: 120rpx;
    height: 80rpx;
  }
}
```

### 大屏适配 (iPhone Plus)
```css
@media (min-width: 414px) {
  .container {
    max-width: 750rpx;
    margin: 0 auto;
  }
}
```

---

**设计要点**:
1. 扫码服务突出显示，引导用户使用
2. 四大板块色彩区分明显，易于识别
3. 信息流设计简洁，便于快速浏览
4. 所有交互元素都有明确的视觉反馈
