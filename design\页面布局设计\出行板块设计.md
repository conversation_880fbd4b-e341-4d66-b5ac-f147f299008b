# 出行板块设计规范

## 📱 页面概述

出行板块专注于本地交通出行信息对接，包括人找车、车找人、货找车、车找货、代驾等服务。

## 🎨 整体布局

### 页面结构
```
出行页面 (750rpx × 1334rpx)
├── 导航栏 (88rpx)
├── 出行类型筛选栏 (120rpx)
├── 快速发布区域 (100rpx)
├── 出行信息列表 (可滚动)
└── Tab栏 (98rpx)
```

### 主题色彩
- **主色调**: 蓝青渐变 (#4facfe → #00f2fe)
- **浅色背景**: rgba(79, 172, 254, 0.1)
- **按钮色**: #4facfe
- **选中状态**: #00f2fe

## 🧭 导航栏设计

```css
.nav-bar {
  height: 88rpx;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32rpx;
  font-weight: 600;
}
```

## 🚗 出行类型筛选

### 筛选栏设计
```css
.transport-filter {
  background: white;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #EBEDF0;
}

.filter-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}

.filter-type {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx 12rpx;
  border-radius: 12rpx;
  background: #F8F9FA;
  border: 1rpx solid #EBEDF0;
  transition: all 0.2s ease;
}

.filter-type-active {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  border-color: transparent;
  box-shadow: 0 2rpx 8rpx rgba(79, 172, 254, 0.3);
}

.filter-icon {
  width: 32rpx;
  height: 32rpx;
  margin-bottom: 8rpx;
}

.filter-text {
  font-size: 22rpx;
  font-weight: 500;
}
```

### 出行类型选项
- **人找车** (乘客找车)
- **车找人** (司机找乘客)
- **货找车** (货主找车)
- **车找货** (司机找货)
- **代驾** (代驾服务)

## 🚀 快速发布区域

```css
.quick-publish {
  background: rgba(79, 172, 254, 0.05);
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.publish-icon {
  width: 40rpx;
  height: 40rpx;
  color: #4facfe;
}

.publish-text {
  flex: 1;
  font-size: 26rpx;
  color: #191F25;
}

.publish-btn {
  padding: 12rpx 24rpx;
  background: linear-gradient(135deg, #4facfe, #00f2fe);
  color: white;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}
```

## 🚙 出行信息卡片

### 基础卡片设计
```css
.transport-card {
  background: white;
  border-radius: 12rpx;
  padding: 24rpx;
  margin: 0 30rpx 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  border-left: 4rpx solid #4facfe;
}

.transport-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.transport-type {
  padding: 6rpx 12rpx;
  border-radius: 6rpx;
  font-size: 20rpx;
  font-weight: 500;
  color: white;
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.transport-time {
  font-size: 22rpx;
  color: #8B9197;
}

.route-section {
  margin-bottom: 16rpx;
}

.route-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 12rpx;
}

.route-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  flex-shrink: 0;
}

.route-start {
  background: #52C41A;
}

.route-end {
  background: #FA5151;
}

.route-text {
  font-size: 26rpx;
  color: #191F25;
  font-weight: 500;
}

.route-line {
  width: 2rpx;
  height: 20rpx;
  background: #EBEDF0;
  margin-left: 5rpx;
}
```

## 👥 人找车卡片

### 乘客信息卡片
```css
.passenger-card {
  border-left-color: #4facfe;
}

.passenger-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.passenger-count {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: #191F25;
}

.passenger-icon {
  width: 20rpx;
  height: 20rpx;
  color: #4facfe;
}

.departure-time {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: #191F25;
}

.time-icon {
  width: 20rpx;
  height: 20rpx;
  color: #4facfe;
}

.price-offer {
  display: flex;
  align-items: baseline;
  gap: 6rpx;
  margin-bottom: 16rpx;
}

.price-amount {
  font-size: 32rpx;
  font-weight: 700;
  color: #4facfe;
}

.price-unit {
  font-size: 22rpx;
  color: #8B9197;
}

.passenger-requirements {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.requirement-tag {
  padding: 4rpx 8rpx;
  background: rgba(79, 172, 254, 0.1);
  color: #4facfe;
  border-radius: 4rpx;
  font-size: 20rpx;
}
```

## 🚗 车找人卡片

### 司机信息卡片
```css
.driver-card {
  border-left-color: #00f2fe;
}

.driver-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.driver-avatar {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #F0F0F0;
}

.driver-details {
  flex: 1;
}

.driver-name {
  font-size: 26rpx;
  font-weight: 600;
  color: #191F25;
  margin-bottom: 4rpx;
}

.driver-rating {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.rating-stars {
  display: flex;
  gap: 2rpx;
}

.star {
  width: 14rpx;
  height: 14rpx;
  color: #FFB800;
}

.rating-score {
  font-size: 20rpx;
  color: #8B9197;
}

.vehicle-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.vehicle-details {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.vehicle-icon {
  width: 24rpx;
  height: 24rpx;
  color: #4facfe;
}

.vehicle-text {
  font-size: 24rpx;
  color: #191F25;
}

.available-seats {
  display: flex;
  align-items: center;
  gap: 6rpx;
  font-size: 24rpx;
  color: #52C41A;
  font-weight: 500;
}

.seat-icon {
  width: 20rpx;
  height: 20rpx;
}
```

## 📦 货运信息卡片

### 货找车卡片
```css
.cargo-card {
  border-left-color: #4facfe;
}

.cargo-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.cargo-weight {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: #191F25;
}

.weight-icon {
  width: 20rpx;
  height: 20rpx;
  color: #4facfe;
}

.cargo-type {
  padding: 4rpx 8rpx;
  background: rgba(79, 172, 254, 0.1);
  color: #4facfe;
  border-radius: 4rpx;
  font-size: 20rpx;
}

.cargo-requirements {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.cargo-requirement {
  padding: 4rpx 8rpx;
  background: rgba(79, 172, 254, 0.1);
  color: #4facfe;
  border-radius: 4rpx;
  font-size: 20rpx;
}

.loading-time {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: #191F25;
  margin-bottom: 16rpx;
}

.loading-icon {
  width: 20rpx;
  height: 20rpx;
  color: #4facfe;
}
```

### 车找货卡片
```css
.truck-card {
  border-left-color: #00f2fe;
}

.truck-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.truck-type {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: #191F25;
}

.truck-icon {
  width: 24rpx;
  height: 24rpx;
  color: #4facfe;
}

.truck-capacity {
  display: flex;
  align-items: center;
  gap: 6rpx;
  font-size: 24rpx;
  color: #52C41A;
  font-weight: 500;
}

.capacity-icon {
  width: 20rpx;
  height: 20rpx;
}

.truck-features {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.truck-feature {
  padding: 4rpx 8rpx;
  background: rgba(79, 172, 254, 0.1);
  color: #4facfe;
  border-radius: 4rpx;
  font-size: 20rpx;
}
```

## 🚕 代驾服务卡片

```css
.driver-service-card {
  border-left-color: #00f2fe;
}

.service-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.service-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #F0F0F0;
}

.service-details {
  flex: 1;
}

.service-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #191F25;
  margin-bottom: 6rpx;
}

.service-experience {
  font-size: 22rpx;
  color: #8B9197;
  margin-bottom: 4rpx;
}

.service-rating {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.service-price {
  display: flex;
  align-items: baseline;
  gap: 6rpx;
  margin-bottom: 16rpx;
}

.price-starting {
  font-size: 22rpx;
  color: #8B9197;
}

.price-amount {
  font-size: 32rpx;
  font-weight: 700;
  color: #4facfe;
}

.price-unit {
  font-size: 22rpx;
  color: #8B9197;
}

.service-areas {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.area-tag {
  padding: 4rpx 8rpx;
  background: rgba(79, 172, 254, 0.1);
  color: #4facfe;
  border-radius: 4rpx;
  font-size: 20rpx;
}
```

## 📱 交互设计

### 卡片交互
```css
.transport-card:active {
  transform: scale(0.98);
  transition: transform 0.1s ease;
}

.contact-btn:active {
  background: linear-gradient(135deg, #3d8bfe, #00d4fe);
}
```

### 筛选动画
```css
.filter-type {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.filter-type:active {
  transform: scale(0.95);
}
```

## 🎯 特殊功能

### 实时状态标识
```css
.status-online {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 16rpx;
  height: 16rpx;
  background: #52C41A;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-busy {
  background: #FFB800;
}

.status-offline {
  background: #8B9197;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}
```

### 紧急出行标识
```css
.urgent-transport {
  background: #FA5151;
  color: white;
  padding: 2rpx 6rpx;
  border-radius: 4rpx;
  font-size: 18rpx;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.5; }
}
```

---

**设计要点**:
1. 蓝青色主题体现出行的流动性和科技感
2. 路线显示清晰，起点终点一目了然
3. 不同出行类型有专门的卡片设计
4. 实时状态和紧急标识帮助用户快速判断
