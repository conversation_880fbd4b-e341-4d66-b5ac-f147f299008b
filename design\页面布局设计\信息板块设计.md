# 信息板块设计规范

## 📱 页面概述

信息板块是本地生活信息发布和浏览的核心区域，包括招聘、房屋、二手、其他等分类信息。

## 🎨 整体布局

### 页面结构
```
信息页面 (750rpx × 1334rpx)
├── 导航栏 (88rpx)
├── 分类筛选栏 (100rpx)
├── 搜索栏 (80rpx)
├── 信息列表 (可滚动)
└── Tab栏 (98rpx)
```

### 主题色彩
- **主色调**: 粉红渐变 (#f093fb → #f5576c)
- **浅色背景**: rgba(240, 147, 251, 0.1)
- **按钮色**: #f093fb
- **选中状态**: #f5576c

## 🧭 导航栏设计

```css
.nav-bar {
  height: 88rpx;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32rpx;
  font-weight: 600;
}
```

## 🏷️ 分类筛选栏

### 筛选标签设计
```css
.category-filter {
  display: flex;
  padding: 20rpx 30rpx;
  gap: 16rpx;
  background: white;
  border-bottom: 1rpx solid #EBEDF0;
}

.filter-item-active {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  box-shadow: 0 2rpx 8rpx rgba(240, 147, 251, 0.3);
}
```

### 分类选项
- **全部** (默认选中)
- **招聘** 
- **房屋**
- **二手**
- **其他**

## 📰 信息卡片设计

### 基础卡片布局
```css
.info-card {
  background: white;
  border-radius: 12rpx;
  padding: 24rpx;
  margin: 0 30rpx 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.info-category {
  padding: 6rpx 12rpx;
  border-radius: 6rpx;
  font-size: 20rpx;
  font-weight: 500;
  color: white;
  background: linear-gradient(135deg, #f093fb, #f5576c);
}

.info-time {
  font-size: 22rpx;
  color: #8B9197;
}

.info-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #191F25;
  line-height: 1.4;
  margin-bottom: 12rpx;
}

.info-content {
  font-size: 24rpx;
  color: #8B9197;
  line-height: 1.5;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.info-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-location {
  display: flex;
  align-items: center;
  gap: 6rpx;
  font-size: 22rpx;
  color: #8B9197;
}

.info-contact {
  display: flex;
  align-items: center;
  gap: 6rpx;
  font-size: 22rpx;
  color: #f093fb;
  font-weight: 500;
}
```

## 💼 招聘信息卡片

### 特殊设计元素
```css
.job-card {
  border-left: 4rpx solid #f093fb;
}

.job-salary {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 12rpx;
}

.salary-amount {
  font-size: 32rpx;
  font-weight: 700;
  color: #f5576c;
}

.salary-unit {
  font-size: 22rpx;
  color: #8B9197;
}

.job-requirements {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.requirement-tag {
  padding: 4rpx 8rpx;
  background: rgba(240, 147, 251, 0.1);
  color: #f5576c;
  border-radius: 4rpx;
  font-size: 20rpx;
}

.company-info {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 12rpx;
}

.company-logo {
  width: 40rpx;
  height: 40rpx;
  border-radius: 6rpx;
  background: #F0F0F0;
}

.company-name {
  font-size: 24rpx;
  color: #191F25;
  font-weight: 500;
}
```

## 🏠 房屋信息卡片

### 房屋特殊元素
```css
.house-card {
  border-left: 4rpx solid #f093fb;
}

.house-price {
  display: flex;
  align-items: baseline;
  gap: 6rpx;
  margin-bottom: 12rpx;
}

.price-amount {
  font-size: 32rpx;
  font-weight: 700;
  color: #f5576c;
}

.price-unit {
  font-size: 22rpx;
  color: #8B9197;
}

.house-specs {
  display: flex;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.spec-item {
  display: flex;
  align-items: center;
  gap: 4rpx;
  font-size: 22rpx;
  color: #8B9197;
}

.house-features {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.feature-tag {
  padding: 4rpx 8rpx;
  background: rgba(240, 147, 251, 0.1);
  color: #f5576c;
  border-radius: 4rpx;
  font-size: 20rpx;
}
```

## 🛍️ 二手物品卡片

### 二手商品元素
```css
.secondhand-card {
  border-left: 4rpx solid #f093fb;
}

.item-images {
  display: flex;
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.item-image {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  background: #F0F0F0;
}

.item-price {
  display: flex;
  align-items: baseline;
  gap: 8rpx;
  margin-bottom: 12rpx;
}

.current-price {
  font-size: 32rpx;
  font-weight: 700;
  color: #f5576c;
}

.original-price {
  font-size: 22rpx;
  color: #8B9197;
  text-decoration: line-through;
}

.item-condition {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 12rpx;
}

.condition-tag {
  padding: 4rpx 8rpx;
  background: rgba(240, 147, 251, 0.1);
  color: #f5576c;
  border-radius: 4rpx;
  font-size: 20rpx;
}

.purchase-info {
  font-size: 22rpx;
  color: #8B9197;
}
```

## 📋 其他信息卡片

### 通用信息卡片
```css
.general-card {
  border-left: 4rpx solid #f093fb;
}

.info-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.info-tag {
  padding: 4rpx 8rpx;
  background: rgba(240, 147, 251, 0.1);
  color: #f5576c;
  border-radius: 4rpx;
  font-size: 20rpx;
}

.urgency-high {
  background: #FA5151;
  color: white;
}

.urgency-medium {
  background: #FFB800;
  color: white;
}

.urgency-low {
  background: rgba(240, 147, 251, 0.1);
  color: #f5576c;
}
```

## 🔍 搜索和筛选

### 高级筛选
```css
.advanced-filter {
  background: white;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #EBEDF0;
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.filter-label {
  font-size: 24rpx;
  color: #191F25;
  font-weight: 500;
  min-width: 80rpx;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}

.filter-option {
  padding: 8rpx 16rpx;
  border: 1rpx solid #EBEDF0;
  border-radius: 20rpx;
  font-size: 22rpx;
  color: #8B9197;
  background: white;
}

.filter-option-active {
  background: linear-gradient(135deg, #f093fb, #f5576c);
  color: white;
  border-color: transparent;
}
```

## 📊 信息统计

### 发布统计
```css
.info-stats {
  background: rgba(240, 147, 251, 0.05);
  padding: 20rpx 30rpx;
  display: flex;
  justify-content: space-around;
  text-align: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.stat-number {
  font-size: 32rpx;
  font-weight: 700;
  color: #f5576c;
}

.stat-label {
  font-size: 22rpx;
  color: #8B9197;
}
```

## 🔄 加载和空状态

### 信息加载状态
```css
.info-loading-card {
  background: white;
  border-radius: 12rpx;
  padding: 24rpx;
  margin: 0 30rpx 16rpx;
}

.loading-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.loading-category {
  width: 60rpx;
  height: 24rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4rpx;
}

.loading-time {
  width: 80rpx;
  height: 20rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4rpx;
}
```

## 📱 交互设计

### 卡片交互
```css
.info-card:active {
  transform: scale(0.98);
  transition: transform 0.1s ease;
}

.info-contact:active {
  color: #e8439f;
}
```

### 筛选动画
```css
.filter-option {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.filter-option:active {
  transform: scale(0.95);
}
```

## 🎯 特殊功能

### 置顶信息标识
```css
.pinned-badge {
  position: absolute;
  top: -4rpx;
  right: -4rpx;
  width: 24rpx;
  height: 24rpx;
  background: linear-gradient(135deg, #f093fb, #f5576c);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pinned-icon {
  width: 12rpx;
  height: 12rpx;
  color: white;
}
```

### 紧急信息标识
```css
.urgent-badge {
  background: #FA5151;
  color: white;
  padding: 2rpx 6rpx;
  border-radius: 4rpx;
  font-size: 18rpx;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}
```

---

**设计要点**:
1. 粉红色主题体现信息板块的活跃性
2. 不同信息类型有专门的卡片设计
3. 筛选功能强大，便于用户快速找到需要的信息
4. 紧急和置顶信息有明显的视觉标识
