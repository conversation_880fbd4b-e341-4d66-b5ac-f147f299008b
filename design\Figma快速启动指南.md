# Figma快速启动指南

## 🚀 第一步：创建Figma项目

### 1. 访问Figma
- 打开浏览器，访问 https://www.figma.com/
- 注册账号或使用Google账号登录
- 选择免费的个人版本

### 2. 创建新项目
1. 点击 "New design file"
2. 文件命名：`本地助手小程序设计`
3. 设置团队：Personal (个人)

### 3. 设置画布
1. 选择 Frame 工具 (快捷键 F)
2. 在右侧选择 "Phone" → "iPhone 14"
3. 画布尺寸：390 × 844 px

## 🎨 第二步：建立设计系统

### 1. 创建色彩样式

#### 操作步骤：
1. 创建一个矩形 (快捷键 R)
2. 填充主色 #07C160
3. 右侧面板点击 "Style" 图标
4. 点击 "+" 创建新样式
5. 命名：`Primary/Main`

#### 完整色彩列表：
```
主色系：
- Primary/Main: #07C160
- Primary/Light: #52C41A  
- Primary/Dark: #06AD56

辅助色系：
- Secondary/Main: #10AEFF
- Accent/Main: #FF6B35
- Warning/Main: #FFB800
- Error/Main: #FA5151

文本色系：
- Text/Primary: #191F25
- Text/Secondary: #8B9197
- Text/Placeholder: #C5CAD0

背景色系：
- Background/Primary: #FFFFFF
- Background/Secondary: #F8F9FA
- Border/Main: #EBEDF0

板块色系：
- Shops/Start: #667eea
- Shops/End: #764ba2
- Info/Start: #f093fb
- Info/End: #f5576c
- Transport/Start: #4facfe
- Transport/End: #00f2fe
- Errands/Start: #43e97b
- Errands/End: #38f9d7
```

### 2. 创建文字样式

#### 操作步骤：
1. 选择文字工具 (快捷键 T)
2. 输入示例文字
3. 设置字体：Inter (Figma默认)
4. 设置字号：18px (对应36rpx)
5. 右侧面板创建文字样式
6. 命名：`Heading/XL`

#### 完整文字样式：
```
标题系列：
- Heading/XL: 18px, Bold (36rpx)
- Heading/L: 16px, Bold (32rpx)
- Heading/M: 14px, SemiBold (28rpx)

正文系列：
- Body/L: 13px, Regular (26rpx)
- Body/M: 12px, Regular (24rpx)
- Body/S: 10px, Regular (20rpx)

特殊样式：
- Button/L: 16px, Medium (32rpx)
- Button/M: 14px, Medium (28rpx)
- Button/S: 12px, Medium (24rpx)
- Caption: 10px, Regular (20rpx)
```

### 3. 创建基础组件

#### 按钮组件创建：
1. 创建矩形：宽度120px，高度40px
2. 设置圆角：6px
3. 填充主色：Primary/Main
4. 添加文字："按钮"，样式Button/M，颜色白色
5. 选中所有元素，创建组件 (Ctrl/Cmd + Alt + K)
6. 命名：`Button/Primary`

#### 创建按钮变体：
1. 复制按钮组件3次
2. 修改样式：Secondary, Outline, Text
3. 选中所有按钮，右键 "Combine as Variants"
4. 设置属性：Type = Primary, Secondary, Outline, Text

## 📱 第三步：设计核心页面

### 1. 首页设计

#### 页面结构：
```
首页 (iPhone 14 Frame)
├── 状态栏 (44px高度)
├── 搜索头部 (80px高度)
│   ├── 搜索栏 (圆角25px)
│   └── 扫码按钮 (40px圆形)
├── 扫码服务卡片 (100px高度)
│   ├── 渐变背景 (紫色渐变)
│   ├── 扫码图标 (40px圆形)
│   ├── 服务文字
│   └── 箭头图标
├── 四大板块导航 (2x2网格)
│   ├── 商铺卡片 (蓝紫渐变)
│   ├── 信息卡片 (粉红渐变)
│   ├── 出行卡片 (蓝青渐变)
│   └── 跑腿卡片 (绿青渐变)
├── 快速发布区域
└── 信息流列表
```

#### 设计要点：
- 使用Auto Layout进行布局
- 设置合适的间距 (16px, 24px)
- 添加卡片阴影效果
- 使用组件库中的元素

### 2. 商铺板块设计

#### 页面结构：
```
商铺页面
├── 导航栏 (标题：商铺)
├── 分类筛选栏
│   ├── 全部 (默认选中)
│   ├── 实体店
│   ├── 网店
│   ├── 摊贩
│   └── 师傅
├── 搜索栏
└── 商家卡片列表
    ├── 商家头像 (48px圆形)
    ├── 商家信息
    │   ├── 店铺名称
    │   ├── 主营业务
    │   ├── 营业时间
    │   └── 距离显示
    ├── 认证标识
    └── 联系按钮
```

## 🔧 第四步：添加交互原型

### 1. 创建页面连接
1. 切换到 Prototype 模式
2. 选择可点击元素
3. 拖拽连接到目标页面
4. 设置动画：Smart Animate
5. 设置时长：300ms

### 2. 常用交互设置
```
导航交互：
- Tab切换：Instant
- 页面跳转：Smart Animate, 300ms
- 返回操作：Smart Animate, 300ms

按钮交互：
- 点击反馈：Scale 95%, 100ms
- 状态切换：Dissolve, 200ms

列表交互：
- 滚动：Scroll
- 下拉刷新：Move In, 400ms
```

## 📋 第五步：设计验收

### 设计完整性检查
- [ ] 所有必需页面已设计
- [ ] 组件库建立完整
- [ ] 交互流程清晰
- [ ] 设计规范统一

### 技术可行性检查
- [ ] 设计符合小程序规范
- [ ] 组件可以实现
- [ ] 性能考虑充分
- [ ] 兼容性良好

### 用户体验检查
- [ ] 操作流程简单
- [ ] 信息层次清晰
- [ ] 视觉效果舒适
- [ ] 错误处理友好

## 🎯 设计技巧

### Figma快捷键
```
基础操作：
- V: 选择工具
- R: 矩形工具
- T: 文字工具
- F: 框架工具

组件操作：
- Ctrl/Cmd + Alt + K: 创建组件
- Ctrl/Cmd + Alt + B: 创建组件实例
- Alt + 拖拽: 复制元素

布局操作：
- Shift + A: 自动布局
- Ctrl/Cmd + G: 编组
- Ctrl/Cmd + Shift + G: 取消编组
```

### 设计最佳实践
1. **保持一致性** - 使用统一的样式和组件
2. **合理命名** - 使用清晰的图层和组件命名
3. **组织结构** - 保持清晰的文件结构
4. **版本管理** - 定期保存和备份设计文件

---

**准备就绪！** 现在您可以开始在Figma中创建"本地助手"小程序的设计了。如有任何问题，随时联系我获取帮助。
