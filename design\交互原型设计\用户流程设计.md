# 用户流程设计

## 🎯 核心用户流程

### 1. 用户注册/登录流程
```mermaid
graph TD
    A[打开小程序] --> B{是否已登录}
    B -->|是| C[进入首页]
    B -->|否| D[微信授权登录]
    D --> E[获取用户信息]
    E --> F[完善个人资料]
    F --> C[进入首页]
```

### 2. 信息浏览流程
```mermaid
graph TD
    A[首页] --> B[选择板块]
    B --> C[浏览信息列表]
    C --> D[点击感兴趣的信息]
    D --> E[查看详情页面]
    E --> F{是否联系}
    F -->|是| G[选择联系方式]
    F -->|否| H[返回列表]
    G --> I[发起联系]
    H --> C
```

### 3. 信息发布流程
```mermaid
graph TD
    A[首页/板块页] --> B[点击发布按钮]
    B --> C[选择发布类型]
    C --> D[填写基本信息]
    D --> E[上传图片]
    E --> F[选择位置]
    F --> G[设置联系方式]
    G --> H[预览信息]
    H --> I{确认发布}
    I -->|是| J[提交审核]
    I -->|否| K[返回编辑]
    J --> L[发布成功]
    K --> D
```

### 4. 扫码呼叫流程
```mermaid
graph TD
    A[首页扫码卡片] --> B[点击扫码]
    B --> C[调用相机扫码]
    C --> D{扫码结果}
    D -->|有效二维码| E[解析服务信息]
    D -->|无效二维码| F[提示错误]
    E --> G[显示服务详情]
    G --> H[选择联系方式]
    H --> I[发起联系]
    F --> C
```

## 📱 页面交互流程

### 首页交互流程
```
首页交互点:
├── 搜索栏 → 搜索页面
├── 扫码按钮 → 扫码功能
├── 扫码服务卡片 → 扫码功能
├── 商铺导航 → 商铺板块
├── 信息导航 → 信息板块
├── 出行导航 → 出行板块
├── 跑腿导航 → 跑腿板块
├── 快速发布按钮 → 发布页面
├── 信息流卡片 → 对应详情页
└── Tab栏 → 对应板块页面
```

### 板块页面交互流程
```
板块页面交互点:
├── 分类筛选 → 筛选结果
├── 搜索栏 → 搜索页面
├── 信息卡片 → 详情页面
├── 联系按钮 → 联系方式选择
├── 发布按钮 → 发布页面
└── Tab栏 → 其他板块
```

### 详情页面交互流程
```
详情页面交互点:
├── 返回按钮 → 上一页面
├── 分享按钮 → 分享功能
├── 收藏按钮 → 收藏功能
├── 图片轮播 → 图片预览
├── 地图位置 → 地图导航
├── 联系方式 → 联系功能
└── 底部操作 → 对应功能
```

## 🔄 状态转换设计

### 页面加载状态
```css
/* 加载状态流程 */
.page-loading {
  /* 初始状态：显示骨架屏 */
  opacity: 1;
  transition: opacity 0.3s ease;
}

.page-loaded {
  /* 加载完成：显示内容 */
  opacity: 1;
}

.page-error {
  /* 错误状态：显示错误页面 */
  opacity: 1;
}
```

### 按钮交互状态
```css
/* 按钮状态流程 */
.btn {
  /* 默认状态 */
  transform: scale(1);
  transition: all 0.2s ease;
}

.btn:active {
  /* 点击状态 */
  transform: scale(0.98);
}

.btn:disabled {
  /* 禁用状态 */
  opacity: 0.5;
  pointer-events: none;
}

.btn-loading {
  /* 加载状态 */
  position: relative;
  color: transparent;
}

.btn-loading::after {
  /* 加载动画 */
  content: '';
  position: absolute;
  width: 20rpx;
  height: 20rpx;
  border: 2rpx solid currentColor;
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
```

### 表单验证状态
```css
/* 表单状态流程 */
.form-field {
  /* 默认状态 */
  border-color: #EBEDF0;
  transition: all 0.2s ease;
}

.form-field:focus {
  /* 聚焦状态 */
  border-color: #07C160;
  box-shadow: 0 0 0 4rpx rgba(7, 193, 96, 0.1);
}

.form-field.error {
  /* 错误状态 */
  border-color: #FA5151;
  box-shadow: 0 0 0 4rpx rgba(250, 81, 81, 0.1);
}

.form-field.success {
  /* 成功状态 */
  border-color: #52C41A;
  box-shadow: 0 0 0 4rpx rgba(82, 196, 26, 0.1);
}
```

## 🎨 动画设计

### 页面切换动画
```css
/* 页面进入动画 */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 页面退出动画 */
@keyframes slideOutLeft {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(-100%);
    opacity: 0;
  }
}

/* 模态框动画 */
@keyframes fadeInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 卡片加载动画 */
@keyframes cardSlideIn {
  from {
    transform: translateY(20rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
```

### 微交互动画
```css
/* 点击反馈动画 */
@keyframes tapFeedback {
  0% { transform: scale(1); }
  50% { transform: scale(0.98); }
  100% { transform: scale(1); }
}

/* 成功反馈动画 */
@keyframes successPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* 错误摇摆动画 */
@keyframes errorShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-10rpx); }
  75% { transform: translateX(10rpx); }
}

/* 加载脉冲动画 */
@keyframes loadingPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}
```

## 📲 手势交互设计

### 滑动手势
```javascript
// 左右滑动切换
const swipeGestures = {
  // 图片轮播滑动
  imageCarousel: {
    direction: 'horizontal',
    threshold: 50, // 50rpx
    sensitivity: 0.3
  },
  
  // Tab切换滑动
  tabSwitch: {
    direction: 'horizontal',
    threshold: 100, // 100rpx
    sensitivity: 0.5
  },
  
  // 返回手势
  backGesture: {
    direction: 'horizontal',
    edge: 'left',
    threshold: 30, // 30rpx
    sensitivity: 0.8
  }
}
```

### 长按手势
```javascript
// 长按交互
const longPressGestures = {
  // 卡片长按菜单
  cardLongPress: {
    duration: 500, // 500ms
    actions: ['收藏', '分享', '举报']
  },
  
  // 图片长按保存
  imageLongPress: {
    duration: 800, // 800ms
    actions: ['保存图片', '分享图片']
  }
}
```

## 🔔 反馈设计

### 成功反馈
```javascript
// 成功操作反馈
const successFeedback = {
  // 发布成功
  publishSuccess: {
    type: 'toast',
    message: '发布成功，等待审核',
    duration: 2000,
    icon: 'success'
  },
  
  // 收藏成功
  favoriteSuccess: {
    type: 'vibration',
    pattern: 'light',
    toast: '已收藏'
  },
  
  // 联系成功
  contactSuccess: {
    type: 'toast',
    message: '联系方式已复制',
    duration: 1500
  }
}
```

### 错误反馈
```javascript
// 错误操作反馈
const errorFeedback = {
  // 网络错误
  networkError: {
    type: 'toast',
    message: '网络连接失败，请检查网络',
    duration: 3000,
    icon: 'error'
  },
  
  // 表单验证错误
  validationError: {
    type: 'shake',
    element: '.form-field',
    message: '请填写必填信息'
  },
  
  // 权限错误
  permissionError: {
    type: 'modal',
    title: '需要授权',
    message: '请在设置中开启相机权限',
    actions: ['取消', '去设置']
  }
}
```

## 🎯 用户引导设计

### 首次使用引导
```javascript
// 新用户引导流程
const onboardingFlow = [
  {
    target: '.scan-service-card',
    title: '扫码呼叫服务',
    description: '扫描商家二维码，快速获取联系方式',
    position: 'bottom'
  },
  {
    target: '.nav-grid',
    title: '四大服务板块',
    description: '商铺、信息、出行、跑腿，满足您的本地生活需求',
    position: 'bottom'
  },
  {
    target: '.publish-section',
    title: '快速发布',
    description: '一键发布您的信息，让更多人看到',
    position: 'top'
  }
]
```

### 功能提示
```javascript
// 功能提示设计
const featureTips = {
  // 首次发布提示
  firstPublish: {
    trigger: 'first_publish_attempt',
    content: '发布信息需要审核，通常在2小时内完成',
    type: 'tooltip',
    position: 'top'
  },
  
  // 扫码功能提示
  scanTip: {
    trigger: 'scan_button_hover',
    content: '对准商家二维码扫描',
    type: 'tooltip',
    position: 'bottom'
  }
}
```

## 📊 性能优化

### 页面加载优化
```javascript
// 页面加载策略
const loadingStrategy = {
  // 首页优先加载
  homepage: {
    priority: 'high',
    preload: ['images', 'critical_data'],
    lazy: ['feed_data', 'non_critical_images']
  },
  
  // 板块页面按需加载
  sectionPages: {
    priority: 'medium',
    preload: ['category_data'],
    lazy: ['detail_images', 'extended_info']
  },
  
  // 详情页面渐进加载
  detailPages: {
    priority: 'medium',
    preload: ['basic_info', 'main_image'],
    lazy: ['gallery_images', 'related_info']
  }
}
```

### 交互响应优化
```javascript
// 交互响应策略
const responseStrategy = {
  // 即时反馈
  immediate: {
    actions: ['button_press', 'input_focus'],
    feedback: 'visual',
    delay: 0
  },
  
  // 快速反馈
  fast: {
    actions: ['form_submit', 'data_save'],
    feedback: 'toast',
    delay: 100
  },
  
  // 延迟反馈
  delayed: {
    actions: ['network_request', 'file_upload'],
    feedback: 'loading',
    delay: 300
  }
}
```

---

**设计原则**:
1. **一致性** - 所有交互保持一致的视觉和行为模式
2. **可预测性** - 用户能够预期交互的结果
3. **即时反馈** - 每个操作都有明确的反馈
4. **容错性** - 提供清晰的错误信息和恢复路径
5. **可访问性** - 考虑不同用户的使用需求
