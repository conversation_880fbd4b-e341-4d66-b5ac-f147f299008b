# Figma组件库设计指南

## 🎨 组件库概述

本指南将帮助您在Figma中建立"本地助手"小程序的完整组件库，确保设计的一致性和可复用性。

## 📦 组件库结构

### 1. 基础组件 (Basic Components)

#### 1.1 按钮组件 (Button)
```
变体 (Variants):
├── Type: Primary, Secondary, Outline, Text
├── Size: Small, Medium, Large
├── State: Default, Hover, Active, Disabled
└── Width: Auto, Full
```

**设计规范:**
- Primary: 背景#07C160，文字白色
- Secondary: 背景#10AEFF，文字白色  
- Outline: 边框#07C160，文字#07C160
- Text: 无背景，文字#07C160

#### 1.2 输入框组件 (Input)
```
变体 (Variants):
├── Type: Text, Number, Password, Search
├── State: Default, Focus, Error, Disabled
└── Size: Small, Medium, Large
```

#### 1.3 标签组件 (Tag)
```
变体 (Variants):
├── Type: Default, Primary, Success, Warning, Error
├── Size: Small, Medium
└── Shape: Rounded, Pill
```

#### 1.4 卡片组件 (Card)
```
变体 (Variants):
├── Type: Basic, Elevated, Outlined
├── Padding: Small, Medium, Large
└── Corner: Rounded, Sharp
```

### 2. 导航组件 (Navigation Components)

#### 2.1 Tab栏组件 (TabBar)
```
组件包含:
├── 首页 (Home)
├── 商铺 (Shops) 
├── 信息 (Information)
├── 出行 (Transportation)
└── 跑腿 (Errands)

状态:
├── Default (未选中)
└── Active (选中)
```

#### 2.2 导航栏组件 (NavBar)
```
变体 (Variants):
├── Type: Default, Search, Back
├── Background: Primary, White, Transparent
└── Title: Show, Hide
```

#### 2.3 分类筛选组件 (CategoryFilter)
```
变体 (Variants):
├── Style: Pills, Tabs, Chips
├── State: Default, Active
└── Color: 根据板块使用不同色彩
```

### 3. 内容组件 (Content Components)

#### 3.1 商家卡片 (ShopCard)
```
组件元素:
├── 头像 (Avatar)
├── 商家名称 (Name)
├── 主营业务 (Business)
├── 营业时间 (Hours)
├── 距离 (Distance)
├── 认证标识 (Verified)
├── 联系按钮 (Contact)
└── 评分 (Rating) [可选]
```

#### 3.2 信息卡片 (InfoCard)
```
组件元素:
├── 分类标签 (Category)
├── 标题 (Title)
├── 内容摘要 (Summary)
├── 发布时间 (Time)
├── 位置信息 (Location)
├── 联系方式 (Contact)
└── 图片 (Images) [可选]
```

#### 3.3 出行卡片 (TransportCard)
```
组件元素:
├── 出行类型 (Type)
├── 起点终点 (Route)
├── 出发时间 (Time)
├── 价格信息 (Price)
├── 车辆信息 (Vehicle) [可选]
├── 联系方式 (Contact)
└── 认证状态 (Verified)
```

#### 3.4 跑腿卡片 (ErrandCard)
```
组件元素:
├── 任务类型 (Type)
├── 任务描述 (Description)
├── 时间要求 (TimeReq)
├── 报酬信息 (Reward)
├── 紧急程度 (Urgency)
├── 联系方式 (Contact)
└── 位置信息 (Location)
```

### 4. 功能组件 (Functional Components)

#### 4.1 搜索组件 (Search)
```
组件元素:
├── 搜索图标 (Icon)
├── 输入框 (Input)
├── 占位文字 (Placeholder)
├── 清除按钮 (Clear)
└── 搜索按钮 (Submit)
```

#### 4.2 扫码服务组件 (ScanService)
```
组件元素:
├── 扫码图标 (ScanIcon)
├── 服务标题 (Title)
├── 服务描述 (Description)
├── 箭头图标 (Arrow)
└── 渐变背景 (Gradient)
```

#### 4.3 板块导航组件 (SectionNav)
```
组件元素:
├── 图标背景 (IconBg) - 渐变色
├── 功能图标 (Icon)
├── 板块名称 (Name)
├── 板块描述 (Description)
└── 点击效果 (Hover)
```

### 5. 状态组件 (State Components)

#### 5.1 加载组件 (Loading)
```
变体 (Variants):
├── Type: Spinner, Skeleton, Progress
├── Size: Small, Medium, Large
└── Color: Primary, Secondary, Gray
```

#### 5.2 空状态组件 (Empty)
```
组件元素:
├── 插图 (Illustration)
├── 标题 (Title)
├── 描述 (Description)
└── 操作按钮 (Action) [可选]
```

#### 5.3 错误状态组件 (Error)
```
组件元素:
├── 错误图标 (Icon)
├── 错误信息 (Message)
├── 重试按钮 (Retry)
└── 返回按钮 (Back) [可选]
```

## 🎯 设计系统建立步骤

### 第一步：创建色彩样式
1. 在Figma中创建Local Styles
2. 添加所有主色调和辅助色
3. 创建板块专用色彩
4. 设置文本和背景色

### 第二步：建立文字样式
1. 创建字体样式库
2. 设置各级标题样式
3. 定义正文和辅助文字
4. 配置行高和字间距

### 第三步：设计基础组件
1. 按照组件清单逐一设计
2. 创建组件变体 (Variants)
3. 设置组件属性 (Properties)
4. 添加组件描述和使用说明

### 第四步：组合复杂组件
1. 使用基础组件组合
2. 创建页面级组件
3. 设置自动布局 (Auto Layout)
4. 配置响应式约束

## 📐 组件设计规范

### 尺寸规范
```
最小点击区域: 44px × 44px
按钮高度: 32px (小), 40px (中), 48px (大)
输入框高度: 40px (小), 48px (中), 56px (大)
卡片最小高度: 80px
图标尺寸: 16px, 20px, 24px, 32px
```

### 间距规范
```
组件内边距: 8px, 12px, 16px, 20px
组件外边距: 8px, 16px, 24px, 32px
文字行间距: 1.4 - 1.6倍字号
段落间距: 16px - 24px
```

### 状态设计
```
默认状态: 正常显示
悬停状态: 轻微高亮或阴影
激活状态: 明显的视觉反馈
禁用状态: 50%透明度
加载状态: 骨架屏或加载动画
```

## 🔧 Figma操作技巧

### 组件创建
1. 选中设计元素
2. 右键选择 "Create Component"
3. 命名组件 (使用 / 分组)
4. 设置组件描述

### 变体创建
1. 选中多个相似组件
2. 右键选择 "Combine as Variants"
3. 设置变体属性名称
4. 配置默认变体

### 自动布局
1. 选中容器元素
2. 按 Shift + A 启用自动布局
3. 设置方向、间距、对齐
4. 配置填充和约束

### 样式管理
1. 创建 Local Styles
2. 使用一致的命名规范
3. 定期整理和更新
4. 与团队同步样式库

## 📋 质量检查清单

### 组件完整性
- [ ] 所有必需组件已创建
- [ ] 组件变体覆盖所有状态
- [ ] 组件命名规范统一
- [ ] 组件描述清晰完整

### 设计一致性
- [ ] 色彩使用符合规范
- [ ] 字体样式保持一致
- [ ] 间距系统统一应用
- [ ] 圆角和阴影规范

### 技术可行性
- [ ] 组件设计可实现
- [ ] 交互逻辑清晰
- [ ] 性能考虑充分
- [ ] 兼容性良好

## 📱 页面设计清单

### 核心页面 (必须设计)
- [ ] 首页 (index) - 主入口页面
- [ ] 商铺板块 (shops) - 商家信息展示
- [ ] 信息板块 (information) - 生活信息发布
- [ ] 出行板块 (transportation) - 出行信息对接
- [ ] 跑腿板块 (errands) - 跑腿服务信息

### 功能页面 (必须设计)
- [ ] 搜索页面 (search) - 全局搜索功能
- [ ] 发布页面 (publish) - 信息发布入口
- [ ] 个人中心 (profile) - 用户个人信息

### 详情页面 (必须设计)
- [ ] 商家详情 (shop-detail) - 商家信息详情
- [ ] 信息详情 (info-detail) - 生活信息详情
- [ ] 出行详情 (transport-detail) - 出行信息详情
- [ ] 跑腿详情 (errand-detail) - 跑腿任务详情

### 特殊功能页面 (必须设计)
- [ ] 扫码呼叫 (call-service) - 扫码后的服务页面
- [ ] 位置选择 (location-picker) - 地理位置选择
- [ ] 图片预览 (image-preview) - 图片查看功能

### 状态页面 (建议设计)
- [ ] 加载页面 (loading) - 数据加载状态
- [ ] 错误页面 (error) - 错误处理页面
- [ ] 空状态页面 (empty) - 无数据状态
- [ ] 网络异常 (network-error) - 网络问题页面

## 🎨 设计交付清单

### Figma文件结构
```
本地助手小程序设计/
├── 📄 Cover (封面页)
├── 🎨 Design System (设计系统)
│   ├── Colors (色彩)
│   ├── Typography (字体)
│   ├── Components (组件库)
│   └── Icons (图标库)
├── 📱 Pages (页面设计)
│   ├── Core Pages (核心页面)
│   ├── Function Pages (功能页面)
│   ├── Detail Pages (详情页面)
│   └── State Pages (状态页面)
├── 🔄 Flows (用户流程)
│   ├── User Journey (用户旅程)
│   ├── Task Flows (任务流程)
│   └── Error Flows (错误流程)
└── 📋 Handoff (交付文档)
    ├── Specifications (设计规范)
    ├── Assets (切图资源)
    └── Prototype (交互原型)
```

### 最终交付物
1. **Figma设计文件** (.fig格式)
2. **设计规范文档** (PDF格式)
3. **交互原型链接** (Figma Prototype)
4. **切图资源包** (PNG/SVG格式)
5. **设计验收报告** (Markdown格式)

---

**下一步**: 在Figma中开始创建组件库，建议先从基础组件开始，逐步构建完整的设计系统。
