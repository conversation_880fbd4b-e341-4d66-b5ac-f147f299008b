# 商铺板块设计规范

## 📱 页面概述

商铺板块展示本地商家信息，包括实体店、网店、摊贩、师傅等，为用户提供便捷的商家查找和联系服务。

## 🎨 整体布局

### 页面结构
```
商铺页面 (750rpx × 1334rpx)
├── 导航栏 (88rpx)
├── 分类筛选栏 (100rpx)
├── 搜索栏 (80rpx)
├── 商家列表 (可滚动)
└── Tab栏 (98rpx)
```

### 主题色彩
- **主色调**: 蓝紫渐变 (#667eea → #764ba2)
- **浅色背景**: rgba(102, 126, 234, 0.1)
- **按钮色**: #667eea
- **选中状态**: #764ba2

## 🧭 导航栏设计

```css
.nav-bar {
  height: 88rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32rpx;
  font-weight: 600;
}
```

## 🏷️ 分类筛选栏

### 布局设计
- **高度**: 100rpx
- **背景**: 白色
- **滚动**: 横向滚动
- **间距**: 16rpx

### 筛选标签设计
```css
.category-filter {
  display: flex;
  padding: 20rpx 30rpx;
  gap: 16rpx;
  background: white;
  border-bottom: 1rpx solid #EBEDF0;
}

.filter-item {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
  font-weight: 500;
  white-space: nowrap;
  transition: all 0.2s ease;
}

.filter-item-default {
  background: #F8F9FA;
  color: #8B9197;
  border: 1rpx solid #EBEDF0;
}

.filter-item-active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
}
```

### 分类选项
- **全部** (默认选中)
- **实体店** 
- **网店**
- **摊贩**
- **师傅**

## 🔍 搜索栏设计

```css
.search-section {
  padding: 20rpx 30rpx;
  background: white;
}

.search-input {
  height: 80rpx;
  background: #F8F9FA;
  border-radius: 40rpx;
  padding: 0 24rpx 0 48rpx;
  font-size: 28rpx;
  position: relative;
}

.search-input::before {
  content: '';
  position: absolute;
  left: 16rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 24rpx;
  height: 24rpx;
  background-image: url('data:image/svg+xml;base64,搜索图标');
}

.search-input::placeholder {
  color: #C5CAD0;
}
```

## 🏪 商家卡片设计

### 卡片布局
- **高度**: 160rpx
- **边距**: 30rpx (左右), 16rpx (上下)
- **圆角**: 12rpx
- **阴影**: 0 2rpx 8rpx rgba(0, 0, 0, 0.05)

### 卡片结构
```css
.shop-card {
  background: white;
  border-radius: 12rpx;
  padding: 24rpx;
  margin: 0 30rpx 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  display: flex;
  gap: 20rpx;
}

.shop-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: #F0F0F0;
  flex-shrink: 0;
}

.shop-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.shop-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 8rpx;
}

.shop-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #191F25;
}

.verified-badge {
  padding: 2rpx 8rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  font-size: 18rpx;
  border-radius: 4rpx;
}

.shop-business {
  font-size: 24rpx;
  color: #8B9197;
  line-height: 1.4;
  margin-bottom: 12rpx;
}

.shop-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.shop-info {
  display: flex;
  gap: 16rpx;
}

.shop-hours,
.shop-distance {
  font-size: 22rpx;
  color: #8B9197;
  display: flex;
  align-items: center;
  gap: 4rpx;
}

.contact-btn {
  padding: 8rpx 16rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-radius: 6rpx;
  font-size: 22rpx;
  font-weight: 500;
}
```

### 商家状态标识
```css
.shop-status {
  display: flex;
  gap: 8rpx;
  margin-bottom: 8rpx;
}

.status-open {
  padding: 2rpx 6rpx;
  background: #52C41A;
  color: white;
  font-size: 18rpx;
  border-radius: 4rpx;
}

.status-closed {
  padding: 2rpx 6rpx;
  background: #FA5151;
  color: white;
  font-size: 18rpx;
  border-radius: 4rpx;
}

.status-break {
  padding: 2rpx 6rpx;
  background: #FFB800;
  color: white;
  font-size: 18rpx;
  border-radius: 4rpx;
}
```

## 📍 位置和距离显示

### 距离计算显示
```css
.distance-info {
  display: flex;
  align-items: center;
  gap: 4rpx;
  font-size: 22rpx;
  color: #8B9197;
}

.distance-icon {
  width: 16rpx;
  height: 16rpx;
  color: #8B9197;
}

.distance-text {
  font-weight: 500;
}

/* 距离颜色区分 */
.distance-near {    /* <500m */
  color: #52C41A;
}

.distance-medium {  /* 500m-2km */
  color: #FFB800;
}

.distance-far {     /* >2km */
  color: #FA5151;
}
```

## 🏆 认证和评级系统

### 认证标识
```css
.certification-badges {
  display: flex;
  gap: 6rpx;
  margin-bottom: 8rpx;
}

.cert-verified {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 2rpx 6rpx;
  border-radius: 4rpx;
  font-size: 18rpx;
}

.cert-premium {
  background: linear-gradient(135deg, #FFB800, #FF8C00);
  color: white;
  padding: 2rpx 6rpx;
  border-radius: 4rpx;
  font-size: 18rpx;
}
```

### 评分显示
```css
.rating-section {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 8rpx;
}

.rating-stars {
  display: flex;
  gap: 2rpx;
}

.star {
  width: 16rpx;
  height: 16rpx;
  color: #FFB800;
}

.rating-score {
  font-size: 22rpx;
  font-weight: 600;
  color: #191F25;
}

.rating-count {
  font-size: 20rpx;
  color: #8B9197;
}
```

## 🔄 加载和空状态

### 加载状态
```css
.loading-card {
  background: white;
  border-radius: 12rpx;
  padding: 24rpx;
  margin: 0 30rpx 16rpx;
  display: flex;
  gap: 20rpx;
}

.loading-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

.loading-content {
  flex: 1;
}

.loading-line {
  height: 24rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4rpx;
  margin-bottom: 12rpx;
}

.loading-line-short {
  width: 60%;
}

.loading-line-long {
  width: 80%;
}
```

### 空状态
```css
.empty-state {
  text-align: center;
  padding: 100rpx 30rpx;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin: 0 auto 24rpx;
  opacity: 0.3;
}

.empty-title {
  font-size: 28rpx;
  color: #8B9197;
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 24rpx;
  color: #C5CAD0;
  line-height: 1.5;
}
```

## 📱 交互设计

### 点击反馈
```css
.shop-card:active {
  transform: scale(0.98);
  transition: transform 0.1s ease;
}

.contact-btn:active {
  background: linear-gradient(135deg, #5a6fd8, #6a42a0);
}
```

### 筛选动画
```css
.filter-item {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.filter-item:active {
  transform: scale(0.95);
}
```

## 🎯 特殊功能

### 呼叫服务商家标识
```css
.call-service-badge {
  position: absolute;
  top: -4rpx;
  right: -4rpx;
  width: 24rpx;
  height: 24rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.call-service-icon {
  width: 12rpx;
  height: 12rpx;
  color: white;
}
```

---

**设计要点**:
1. 蓝紫色主题贯穿整个板块
2. 商家信息层次清晰，重点突出
3. 认证和评级系统增强信任度
4. 距离显示帮助用户快速决策
