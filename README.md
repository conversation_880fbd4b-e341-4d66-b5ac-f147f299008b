# 本地助手微信小程序

## 📖 项目简介

"本地助手"是一个专注于本地生活服务的免费信息发布与查询平台，连接本地用户与商家/服务提供者。这是一个信息撮合平台，不是电商平台，我们只提供信息展示和联系方式，不参与交易流程。

## 🎯 产品定位

- **信息撮合平台**：仅提供信息展示和联系方式，不参与交易流程
- **非电商平台**：不提供在线支付、订单管理、物流跟踪等电商功能
- **轻量化工具**：专注于信息发布、搜索、联系等核心功能

## 🏗️ 技术架构

### 前端技术栈
- **微信小程序原生开发**
- **WXML** - 页面结构
- **WXSS** - 样式设计
- **JavaScript** - 业务逻辑

### 后端技术栈
- **微信云开发**
- **云数据库** - 数据存储
- **云函数** - 业务逻辑处理
- **云存储** - 文件存储

## 📱 核心功能

### 四大板块
1. **商铺板块** - 本地商家信息展示与搜索
2. **信息板块** - 个人信息发布与查询
3. **出行板块** - 本地出行信息对接
4. **跑腿板块** - 本地跑腿服务信息

### 特色功能
- **扫码呼叫服务** - 免注册快速呼叫商家服务
- **位置服务** - 基于地理位置的精准推荐
- **实时搜索** - 全局搜索功能
- **信息发布** - 简单易用的信息发布流程

## 🚀 快速开始

### 环境要求
- Node.js 18.x 或更高版本
- 微信开发者工具最新版本
- 微信小程序账号（已认证）

### 安装步骤

1. **克隆项目**
   ```bash
   git clone [项目地址]
   cd 本地助手小程序
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **配置小程序**
   - 在微信开发者工具中导入项目
   - 填入您的小程序 AppID
   - 配置云开发环境ID

4. **启动开发**
   - 在微信开发者工具中点击"编译"
   - 开始开发调试

## 📁 项目结构

```
本地助手小程序/
├── miniprogram/              # 小程序前端代码
│   ├── pages/               # 页面文件
│   │   ├── index/          # 首页
│   │   ├── shops/          # 商铺板块
│   │   ├── information/    # 信息板块
│   │   ├── transportation/ # 出行板块
│   │   ├── errands/        # 跑腿板块
│   │   ├── publish/        # 发布页面
│   │   ├── profile/        # 个人中心
│   │   ├── search/         # 搜索页面
│   │   └── detail/         # 详情页面
│   ├── components/         # 自定义组件
│   ├── utils/             # 工具函数
│   ├── images/            # 图片资源
│   ├── styles/            # 全局样式
│   ├── app.js             # 小程序逻辑
│   ├── app.json           # 小程序配置
│   └── app.wxss           # 小程序样式
├── cloudfunctions/         # 云函数
├── docs/                  # 项目文档
├── design/                # 设计文件
├── tests/                 # 测试文件
└── README.md              # 项目说明
```

## 🎨 设计规范

### 视觉风格
- **现代简约** - 清新自然的设计风格
- **微信生态** - 符合微信小程序设计规范
- **色彩系统** - 使用统一的色彩规范

### 四大板块色彩
- **商铺板块** - 蓝紫色渐变 (#667eea → #764ba2)
- **信息板块** - 粉红色渐变 (#f093fb → #f5576c)
- **出行板块** - 蓝青色渐变 (#4facfe → #00f2fe)
- **跑腿板块** - 绿青色渐变 (#43e97b → #38f9d7)

## 🔧 开发规范

### 代码规范
- 使用 ES6+ 语法
- 统一的命名规范
- 完善的注释说明
- 模块化开发

### 提交规范
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 样式调整
- refactor: 代码重构

## 📋 开发计划

- [x] 项目初始化与环境配置
- [ ] 高保真UI/UX原型设计
- [ ] 微信小程序前端开发
- [ ] 后端开发与云服务配置
- [ ] 测试与质量保证
- [ ] 部署与上线准备

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系我们

如有问题或建议，请通过以下方式联系：
- 项目Issues
- 邮箱：[联系邮箱]
- 微信：[联系微信]

---

**注意**: 本项目严格遵循微信小程序开发规范，仅支持微信小程序平台。
