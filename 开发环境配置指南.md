# 本地助手小程序开发环境配置指南

## 📋 目录
1. [必需软件安装](#必需软件安装)
2. [微信开发者工具配置](#微信开发者工具配置)
3. [项目初始化](#项目初始化)
4. [云开发环境配置](#云开发环境配置)
5. [开发工具推荐](#开发工具推荐)

## 🛠️ 必需软件安装

### Windows 系统

#### 1. Node.js 安装
- **下载地址**: https://nodejs.org/zh-cn/
- **推荐版本**: LTS 版本 (当前推荐 18.x 或 20.x)
- **安装步骤**:
  1. 下载 Windows Installer (.msi)
  2. 双击运行安装程序
  3. 选择"Add to PATH"选项
  4. 完成安装后，打开命令提示符验证：
     ```cmd
     node --version
     npm --version
     ```

#### 2. Git 安装
- **下载地址**: https://git-scm.com/download/win
- **安装步骤**:
  1. 下载 Git for Windows
  2. 运行安装程序，保持默认设置
  3. 验证安装：
     ```cmd
     git --version
     ```

### macOS 系统

#### 1. 安装 Homebrew (包管理器)
```bash
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
```

#### 2. 安装 Node.js
```bash
brew install node
```

#### 3. 安装 Git
```bash
brew install git
```

## 📱 微信开发者工具配置

### 1. 下载与安装
- **官方下载地址**: https://developers.weixin.qq.com/miniprogram/dev/devtools/download.html
- **系统要求**:
  - Windows 7 及以上
  - macOS 10.11 及以上

### 2. 安装步骤
1. 下载对应系统版本的安装包
2. Windows: 运行 .exe 文件安装
3. macOS: 拖拽到 Applications 文件夹

### 3. 初次配置
1. 启动微信开发者工具
2. 使用微信扫码登录
3. 选择"小程序项目"
4. 等待我们创建项目后导入

## 🚀 项目初始化

### 1. 创建项目目录结构
```
本地助手小程序/
├── miniprogram/          # 小程序前端代码
│   ├── pages/           # 页面文件
│   ├── components/      # 自定义组件
│   ├── utils/          # 工具函数
│   ├── images/         # 图片资源
│   ├── styles/         # 全局样式
│   ├── app.js          # 小程序逻辑
│   ├── app.json        # 小程序配置
│   └── app.wxss        # 小程序样式
├── cloudfunctions/      # 云函数
├── docs/               # 项目文档
├── design/             # 设计文件
├── tests/              # 测试文件
└── README.md           # 项目说明
```

### 2. 获取小程序 AppID
1. 访问 https://mp.weixin.qq.com/
2. 注册小程序账号（需要企业或个人认证）
3. 在"开发" -> "开发设置"中获取 AppID

## ☁️ 云开发环境配置

### 1. 开通云开发
1. 在微信开发者工具中打开项目
2. 点击"云开发"按钮
3. 开通云开发服务
4. 创建环境（建议创建开发环境和生产环境）

### 2. 云开发功能
- **云数据库**: 存储用户信息、商家信息、发布内容等
- **云存储**: 存储图片、文件等资源
- **云函数**: 处理业务逻辑、数据验证等

## 🔧 开发工具推荐

### 1. 代码编辑器
- **VS Code** (推荐)
  - 下载: https://code.visualstudio.com/
  - 推荐插件:
    - WXML - Language Service
    - minapp
    - Prettier - Code formatter

### 2. 设计工具
- **Figma** (在线设计工具，推荐)
  - 网址: https://www.figma.com/
  - 免费版本足够使用
  - 支持团队协作

### 3. 版本控制
- **GitHub Desktop** (图形界面，适合初学者)
  - 下载: https://desktop.github.com/

## 📝 下一步操作

1. ✅ 安装所有必需软件
2. ✅ 配置微信开发者工具
3. ✅ 注册小程序账号并获取 AppID
4. ✅ 开通云开发服务
5. ⏳ 等待项目代码创建完成后导入

## 🆘 常见问题解决

### Q: Node.js 安装后命令行无法识别？
A: 重启命令行工具，或检查环境变量 PATH 是否包含 Node.js 安装路径

### Q: 微信开发者工具无法登录？
A: 确保网络连接正常，尝试使用手机热点或更换网络

### Q: 云开发开通失败？
A: 确保小程序账号已完成认证，个人账号可能有功能限制

## 📞 技术支持

如果在环境配置过程中遇到问题，请提供：
1. 操作系统版本
2. 错误截图或错误信息
3. 已完成的配置步骤

我将为您提供详细的解决方案。
