// 本地助手小程序 - 首页逻辑
const app = getApp()

Page({
  data: {
    userInfo: {},
    hasUserInfo: false,
    canIUse: wx.canIUse('button.open-type.getUserInfo'),
    feedList: [
      {
        id: 1,
        type: 'shops',
        typeName: '商铺',
        title: '老王理发店',
        content: '专业理发20年，价格实惠，技术精湛',
        location: '距您500m',
        publishTime: '2小时前'
      },
      {
        id: 2,
        type: 'information',
        typeName: '信息',
        title: '出售二手电动车',
        content: '9成新电动车，续航60公里，价格面议',
        location: '距您1.2km',
        publishTime: '3小时前'
      },
      {
        id: 3,
        type: 'transportation',
        typeName: '出行',
        title: '明天早上拼车去市区',
        content: '7点出发，还有2个空位，费用AA',
        location: '距您800m',
        publishTime: '5小时前'
      }
    ]
  },

  onLoad: function () {
    console.log('首页加载')
    this.getUserLocation()
    this.loadFeedData()
  },

  onShow: function () {
    console.log('首页显示')
    // 刷新数据
    this.loadFeedData()
  },

  onPullDownRefresh: function () {
    console.log('下拉刷新')
    this.loadFeedData()
    wx.stopPullDownRefresh()
  },

  onReachBottom: function () {
    console.log('上拉加载更多')
    this.loadMoreFeedData()
  },

  // 获取用户位置
  getUserLocation: function () {
    const that = this
    wx.getLocation({
      type: 'gcj02',
      success: function (res) {
        console.log('获取位置成功:', res)
        app.globalData.userLocation = {
          latitude: res.latitude,
          longitude: res.longitude
        }
      },
      fail: function (err) {
        console.log('获取位置失败:', err)
        wx.showModal({
          title: '提示',
          content: '获取位置失败，部分功能可能受限',
          showCancel: false
        })
      }
    })
  },

  // 加载信息流数据
  loadFeedData: function () {
    const that = this
    app.utils.showLoading('加载中...')
    
    // 模拟API调用
    setTimeout(() => {
      // 这里应该调用云函数获取真实数据
      app.utils.hideLoading()
    }, 1000)
  },

  // 加载更多数据
  loadMoreFeedData: function () {
    console.log('加载更多数据')
    // 这里实现分页加载逻辑
  },

  // 扫码功能
  scanCode: function () {
    const that = this
    wx.scanCode({
      success: function (res) {
        console.log('扫码结果:', res)
        that.handleScanResult(res.result)
      },
      fail: function (err) {
        console.log('扫码失败:', err)
        app.utils.showError('扫码失败')
      }
    })
  },

  // 处理扫码结果
  handleScanResult: function (result) {
    console.log('处理扫码结果:', result)
    
    // 验证是否为平台专用二维码
    if (this.isValidQRCode(result)) {
      // 解析二维码内容
      const qrData = this.parseQRCode(result)
      
      if (qrData.type === 'call_service') {
        // 跳转到呼叫服务页面
        wx.navigateTo({
          url: `/pages/call-service/call-service?scene=${qrData.scene}&merchant=${qrData.merchant}`
        })
      } else {
        app.utils.showError('无效的服务二维码')
      }
    } else {
      app.utils.showError('无效的二维码，请扫描平台专用二维码')
    }
  },

  // 验证二维码有效性
  isValidQRCode: function (qrCode) {
    // 检查是否为平台专用格式
    return qrCode.startsWith('local-helper://') || qrCode.includes('local-helper.com')
  },

  // 解析二维码内容
  parseQRCode: function (qrCode) {
    try {
      // 解析二维码参数
      const url = new URL(qrCode)
      return {
        type: url.searchParams.get('type'),
        scene: url.searchParams.get('scene'),
        merchant: url.searchParams.get('merchant')
      }
    } catch (e) {
      console.error('二维码解析失败:', e)
      return null
    }
  },

  // 跳转到搜索页面
  goToSearch: function () {
    wx.navigateTo({
      url: '/pages/search/search'
    })
  },

  // 跳转到各个板块
  goToShops: function () {
    wx.switchTab({
      url: '/pages/shops/shops'
    })
  },

  goToInformation: function () {
    wx.switchTab({
      url: '/pages/information/information'
    })
  },

  goToTransportation: function () {
    wx.switchTab({
      url: '/pages/transportation/transportation'
    })
  },

  goToErrands: function () {
    wx.switchTab({
      url: '/pages/errands/errands'
    })
  },

  // 跳转到发布页面
  goToPublish: function () {
    wx.navigateTo({
      url: '/pages/publish/publish'
    })
  },

  // 快速发布功能
  publishShop: function () {
    wx.navigateTo({
      url: '/pages/publish/publish?type=shop'
    })
  },

  publishInfo: function () {
    wx.navigateTo({
      url: '/pages/publish/publish?type=information'
    })
  },

  publishTransport: function () {
    wx.navigateTo({
      url: '/pages/publish/publish?type=transportation'
    })
  },

  publishErrand: function () {
    wx.navigateTo({
      url: '/pages/publish/publish?type=errand'
    })
  },

  // 跳转到详情页面
  goToDetail: function (e) {
    const item = e.currentTarget.dataset.item
    const detailPages = {
      'shops': '/pages/detail/shop-detail',
      'information': '/pages/detail/info-detail',
      'transportation': '/pages/detail/transport-detail',
      'errands': '/pages/detail/errand-detail'
    }
    
    const url = detailPages[item.type]
    if (url) {
      wx.navigateTo({
        url: `${url}?id=${item.id}`
      })
    }
  }
})
