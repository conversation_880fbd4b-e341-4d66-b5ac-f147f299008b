# 产品需求文档 (Product Requirements Document)

## 1. 文档信息管理

- **1.1 版本历史记录**
  | 版本号 | 修改日期   | 主要变更内容 | 责任人 | 审批状态 |
  | ------ | ---------- | ------------ | ------ | -------- |
  | V1.0   | 2024-01-15 | 初稿创建     | 产品经理 | 已审批 |
  | V1.1   | 2024-01-20 | 完善技术规范和设计标准 | 产品经理 | 待审批 |
- **1.2 文档编写目的与适用范围**
  - **目的**：本文档旨在明确“本地助手”小程序的产品需求，作为产品设计、开发、测试和运营的依据。
  - **范围**：适用于“本地助手”小程序的所有相关方，包括产品、设计、研发、测试及项目管理团队。
- **1.3 相关文档交叉引用与依赖关系**
  - 用户故事地图: `User_Story_Map.md` (待创建)
  - 产品路线图: `Roadmap.md` (待创建)
  - 产品评估指标框架: `Metrics_Framework.md` (待创建)
- **1.4 文档审批流程与签字确认**
  - **审批层级**：
    - **初审**：产品经理自审（功能完整性、逻辑一致性）
    - **技术审查**：技术负责人审查（技术可行性、架构合理性）
    - **设计审查**：设计负责人审查（设计规范、用户体验）
    - **最终审批**：项目负责人最终审批
  - **审批标准**：
    - 功能需求描述清晰完整，无歧义表述
    - 技术架构设计合理，符合平台限制
    - 验收标准明确，可量化可执行
    - 时间节点合理，资源配置可行
  - **变更管理**：
    - 重大变更（影响核心功能或架构）需重新走完整审批流程
    - 一般变更（细节优化）需相关负责人确认
    - 所有变更必须更新版本记录并通知相关团队

## 2. 产品战略概述

- **2.1 产品名称、定位与核心价值主张**
  - **产品名称**：本地助手
  - **产品定位**：一个专注于本地生活服务的免费信息发布与查询平台，连接本地用户与商家/服务提供者。
  - **平台性质明确定义**：
    - **信息撮合平台**：仅提供信息展示和联系方式，不参与交易流程
    - **非电商平台**：不提供在线支付、订单管理、物流跟踪等电商功能
    - **非SaaS平台**：不为商家提供复杂的店铺管理、数据分析等企业级服务
    - **轻量化工具**：专注于信息发布、搜索、联系等核心功能
  - **核心价值主张**：为用户提供便捷、高效、免费的本地生活信息获取与发布渠道，促进本地信息流通与互助。
- **2.2 产品愿景、使命与长期发展目标**
  - **愿景**：成为本地居民首选的生活服务信息平台。
  - **使命**：让本地生活更简单、更便捷。
  - **长期目标**：覆盖更广泛的本地服务领域，提升用户活跃度和信息匹配效率。
- **2.3 独特销售主张（USP）与核心竞争优势**
  - **USP**：免费、纯粹的本地信息互通平台，无中间商赚差价，信息真实可靠。
  - **核心竞争优势**：信息聚合度高、操作简便、专注于本地化服务、用户驱动内容。
- **2.4 目标平台列表**
  - **主要平台**：微信小程序（必须严格遵循微信小程序开发规范）
  - **平台限制说明**：
    - **仅支持微信小程序平台**：不得设计为Web应用或其他平台
    - **必须使用小程序技术栈**：WXML、WXSS、JavaScript，禁止使用HTML、CSS等Web技术
    - **遵循微信设计规范**：界面设计必须符合微信小程序设计指南
    - **功能限制**：受微信小程序平台政策和技术限制约束
    - **扫码功能集成**：集成微信小程序原生扫码API，支持扫描平台专用二维码
  - **严禁平台变更**：未经产品经理明确书面同意，不得将产品设计为其他平台应用
- **2.5 产品核心假设与验证策略**
  - **核心假设1**：用户对免费的本地生活信息平台有需求。
    - **验证策略**：通过MVP版本上线后的用户增长、活跃度及信息发布量进行验证。
  - **核心假设2**：用户愿意通过平台发布和获取各类本地服务信息。
    - **验证策略**：监测各板块信息发布和浏览比例，收集用户反馈。
- **2.6 商业模式概述与盈利模式设计**
  - **商业模式**：永久免费的本地信息撮合平台，不支持在线交易，仅提供线上信息展示和线下交易撮合服务。
  - **盈利模式**：
    - **优惠券/体验券收费**：用户自行扫码付款开通，40元/月、200元/半年、300元/年
    - **广告收入**：在内容卡片中穿插广告位，支持外部链接或纯展示广告
    - **后台管理授权服务**：为特定用户提供后台管理权限，可选择授权内容范围
  - **交易模式**：纯线上撮合，线下洽谈成交，平台不参与资金流转，确保零风险运营
- **2.7 产品生命周期规划与退出策略**
  - **2.7.1 产品生命周期阶段规划**
    - **孵化期（0-3个月）**：MVP开发、核心功能验证、初始用户获取
    - **成长期（3-12个月）**：功能完善、用户规模扩大、商业模式验证
    - **成熟期（12-24个月）**：市场稳定、盈利模式成熟、功能优化迭代
    - **衰退期/转型期（24个月+）**：市场饱和应对、产品转型或升级
  - **2.7.2 各阶段关键目标**
    - **孵化期目标**：DAU达到500+，核心功能使用率>60%
    - **成长期目标**：MAU达到5000+，月收入达到10万+
    - **成熟期目标**：MAU达到2万+，实现盈亏平衡
  - **2.7.3 风险预警与退出策略**
    - **市场风险**：竞品冲击、政策变化、用户需求转移
    - **技术风险**：平台政策变更、技术架构过时
    - **退出条件**：连续6个月MAU下降超过30%，或无法实现盈亏平衡
    - **退出方案**：用户数据导出、服务平稳过渡、合规关停流程

## 3. 深度用户研究

- **3.1 目标用户画像构建**
  - **3.1.1 人口统计学特征**
    - **年龄分布**：18-60岁，核心用户群体20-50岁（占比65%）
    - **性别分布**：男性45%，女性55%，女性在信息发布和商家查询方面更活跃
    - **收入水平**：月收入3000-8000元，以中等收入群体为主
    - **教育背景**：高中及以上学历占比80%，具备基本的移动互联网使用能力
    - **地域分布**：县域市场，城镇化率60-80%的区域,城区人口约60万人，总人口约160万人
  - **3.1.2 心理特征与价值观分析**
    - **实用主义导向**：注重产品的实际价值，对复杂功能不感兴趣
    - **本地归属感强**：对本地社区有强烈认同感，信任本地推荐
    - **价格敏感度高**：对免费服务有偏好，付费意愿相对较低
    - **社交需求明显**：希望通过平台建立本地社交关系
    - **安全意识增强**：对个人信息保护和交易安全要求提高
  - **3.1.3 行为习惯与使用偏好**
    - **使用时间**：晚上19:00-22:00为高峰期，周末使用频率更高
    - **使用场景**：主要在家中、工作间隙、通勤路上使用
    - **操作习惯**：偏好简单直观的界面，不喜欢复杂的操作流程
    - **信息获取方式**：习惯通过搜索和分类浏览获取信息
    - **分享行为**：乐于分享有价值的本地信息给朋友
  - **3.1.4 核心需求痛点与期望价值**
    - **信息获取痛点**：现有平台信息杂乱、广告多、搜索不精准
    - **信息发布痛点**：发布流程复杂、审核时间长、曝光度低
    - **信任建立痛点**：难以判断信息真实性，缺乏有效的信用机制
    - **期望价值**：简单易用、信息真实、本地化精准、完全免费
  - **3.1.5 用户动机分析与决策影响因素**
    - **主要动机**：解决实际生活问题、获取优惠信息、扩展社交圈
    - **决策因素**：信息质量、平台口碑、使用便捷性、安全保障
    - **转换成本**：学习成本、数据迁移成本、社交关系重建成本
  - **3.1.6 C端用户（信息获取者/服务需求方/信息发布者）**
    - **人口统计学特征**：本地常住居民，年龄段覆盖较广，对移动互联网使用熟练。
    - **心理特征与价值观**：追求便捷、高效、高性价比的生活服务，乐于分享和获取本地信息。
    - **行为习惯**：习惯通过线上渠道获取信息，对本地新闻、优惠、服务有较高关注度。
    - **核心需求痛点**：传统信息渠道分散、效率低；部分服务寻找困难；希望有更直接的本地信息交互方式。
    - **用户动机**：解决生活中的实际需求（如找工作、租房、购物、出行、求助等）。
  - **3.1.7 B端用户/服务提供方（商家/师傅/个人服务者/信息发布者）**
    - **特征**：本地实体店主、网店经营者、个体工商户、手艺人、有闲置资源或技能的个人。
    - **核心需求痛点**：获客渠道有限、推广成本高、希望有更直接的方式触达本地潜在客户。
    - **用户动机**：扩大服务范围、增加客源、提升知名度、发布闲置信息。

- **3.2 用户场景深度分析**
  - **3.2.1 核心使用场景详细描述**
    - **场景一：寻找本地商家**
      - **时间**：工作日午休时间、周末购物时间
      - **地点**：办公室、家中、商圈
      - **环境**：需要快速找到附近的餐厅、超市、服务店
      - **情境**："我想找个离公司近的理发店"
      - **用户期望**：快速定位、查看评价、获取联系方式
    - **场景二：发布闲置物品**
      - **时间**：晚上空闲时间、周末整理时间
      - **地点**：家中
      - **环境**：整理物品时发现闲置品
      - **情境**："家里有台用不到的小家电想卖掉"
      - **用户期望**：简单发布、快速成交、安全交易
    - **场景三：寻找跑腿服务**
      - **时间**：工作日白天、紧急情况
      - **地点**：办公室、家中
      - **环境**：无法亲自办事的紧急情况
      - **情境**："临时有事需要有人帮忙取个快递"
      - **用户期望**：快速响应、价格合理、服务可靠
  - **3.2.2 边缘使用场景与异常情况考量**
    - **深夜紧急求助**：医院陪护、紧急用车等特殊需求
    - **节假日服务需求**：春节期间的特殊服务需求
    - **恶劣天气影响**：台风、暴雨等天气对服务的影响
    - **网络环境差**：偏远地区网络不稳定的使用场景
  - **3.2.3 跨平台使用场景与切换需求**
    - **移动端主导**：日常浏览、信息发布、即时沟通
    - **PC端辅助**：商家管理、批量操作、数据分析
    - **平台间切换**：从微信分享进入、向朋友圈分享信息

- **3.3 用户调研数据洞察**
  - **3.3.1 用户满意度调研**
    - **现有平台满意度**：58同城满意度65%，本地论坛满意度78%
    - **主要不满因素**：广告过多（85%）、界面复杂（72%）、信息不准确（68%）
    - **最看重功能**：搜索准确性（92%）、信息真实性（89%）、操作简便性（84%）
  - **3.3.2 使用行为数据**
    - **平均使用时长**：单次15-25分钟，每周使用3-5次
    - **功能使用频率**：搜索功能使用率95%，发布功能使用率35%
    - **信息浏览偏好**：商家信息（45%）、二手交易（25%）、招聘信息（20%）
  - **3.3.3 付费意愿调研**
    - **付费意愿**：仅有15%用户愿意为信息服务付费
    - **可接受费用**：月费用不超过10元的占比80%
    - **付费场景**：紧急服务（60%）、高质量信息（40%）、去广告（35%）

- **3.4 用户反馈收集与分析机制设计**
  - **3.4.1 反馈收集渠道**
    - **应用内反馈**：意见建议入口、问题举报功能、满意度评价
    - **社交媒体监控**：微信群、QQ群、公众号
    - **客服渠道**：在线客服、电话客服、邮件反馈
    - **用户访谈**：定期组织重点用户深度访谈
  - **3.4.2 反馈分析框架**
    - **分类标准**：功能建议、Bug反馈、体验问题、内容质量
    - **优先级评估**：影响用户数量、问题严重程度、解决难度
    - **响应机制**：24小时内确认、7天内给出解决方案
  - **3.4.3 持续改进机制**
    - **月度用户调研**：通过问卷和访谈了解用户需求变化
    - **数据驱动优化**：基于用户行为数据优化产品功能
    - **A/B测试验证**：新功能上线前进行小范围测试
    - **用户共创**：邀请活跃用户参与产品设计讨论

## 4. 市场环境与竞争分析

- **4.1 目标市场规模评估与增长趋势预测**
  - **4.1.1 市场规模分析**
    - **本地生活服务市场**：中国本地生活服务市场规模超过2亿元，年增长率保持在15%以上
    - **分类信息市场**：细分市场规模约500亿元，其中本地化信息服务占比约30%
    - **目标用户群体**：三四线城市及县域市场，潜在用户规模约3-5亿人
  - **4.1.2 增长趋势预测**
    - 下沉市场数字化程度快速提升，年增长率预计25%+
    - 本地化、个性化服务需求持续增长
    - 小程序生态日趋成熟，用户接受度不断提高
  - **4.1.3 市场机会窗口**
    - 传统分类信息平台在下沉市场覆盖不足
    - 用户对简单、免费、纯粹的信息平台有强烈需求
    - 疫情加速了本地生活服务的线上化进程

- **4.2 行业发展趋势与技术演进分析**
  - **4.2.1 行业发展趋势**
    - **本地化服务精细化**：从粗放式信息发布向精准匹配发展
    - **社区化运营**：用户更倾向于信任本地社区推荐
    - **移动优先**：移动端成为本地生活服务的主要入口
    - **内容可信度要求提升**：用户对信息真实性要求越来越高
  - **4.2.2 技术演进分析**
    - **AI技术应用**：智能推荐、资质审核、用户画像分析
    - **LBS技术成熟**：精准定位、距离计算、路径规划
    - **小程序生态完善**：开发成本降低、用户体验提升
    - **云服务普及**：降低了中小平台的技术门槛
  - **4.2.3 用户行为变化**
    - 从被动浏览向主动搜索转变
    - 对即时性、便捷性要求更高
    - 更注重隐私保护和信息安全

- **4.3 竞争格局深度剖析**
  - **4.3.1 直接竞争对手分析**
    - **58同城/赶集网**
      - 优势：品牌知名度高、信息量大、覆盖面广
      - 劣势：界面复杂、广告多、用户体验差、下沉市场渗透不足
    - **本地论坛/贴吧**
      - 优势：用户粘性强、社区氛围好
      - 劣势：信息组织混乱、搜索功能弱、移动端体验差
    - **微信群/QQ群**
      - 优势：用户信任度高、传播速度快
      - 劣势：信息易丢失、无法有效检索、管理困难
  - **4.3.2 间接竞争对手分析**
    - **美团/大众点评**：主要专注餐饮娱乐，与我们的商铺板块有部分重叠
    - **闲鱼**：二手交易平台，与我们的信息板块闲置分类竞争
    - **BOSS直聘/前程无忧**：招聘平台，与我们的招聘分类竞争
  - **4.3.3 竞争态势评估**
    - 大平台在下沉市场存在服务空白
    - 本地化、垂直化平台有发展机会
    - 用户对简洁、免费平台有需求

- **4.4 竞品功能特性对比矩阵**
  
  | 功能特性 | 本地助手 | 58同城 | 本地论坛 | 微信群 |
  |---------|---------|--------|---------|--------|
  | **界面简洁度** | ★★★★★ | ★★☆☆☆ | ★★★☆☆ | ★★★★☆ |
  | **信息分类** | ★★★★★ | ★★★★☆ | ★★☆☆☆ | ★☆☆☆☆ |
  | **搜索功能** | ★★★★☆ | ★★★★☆ | ★★☆☆☆ | ★☆☆☆☆ |
  | **本地化程度** | ★★★★★ | ★★★☆☆ | ★★★★★ | ★★★★★ |
  | **用户信任度** | ★★★☆☆ | ★★★☆☆ | ★★★★☆ | ★★★★★ |
  | **信息时效性** | ★★★★☆ | ★★★☆☆ | ★★☆☆☆ | ★★★★☆ |
  | **移动端体验** | ★★★★★ | ★★★☆☆ | ★★☆☆☆ | ★★★★☆ |
  | **免费程度** | ★★★★★ | ★★☆☆☆ | ★★★★★ | ★★★★★ |

- **4.5 市场差异化策略与竞争壁垒构建**
  - **4.5.1 差异化策略**
    - **产品差异化**：专注免费、纯粹的信息撮合，不做交易闭环
    - **用户体验差异化**：界面简洁、操作便捷、无广告干扰
    - **服务差异化**：深度本地化运营、快速响应用户需求
    - **技术差异化**：基于小程序的轻量级解决方案
  - **4.5.2 竞争壁垒构建**
    - **用户壁垒**：通过优质服务建立用户忠诚度和口碑传播
    - **内容壁垒**：积累高质量的本地信息内容库
    - **技术壁垒**：持续优化算法和用户体验
    - **运营壁垒**：建立高效的内容审核和社区管理机制
  - **4.5.3 护城河建设**
    - **网络效应**：用户和信息越多，平台价值越大
    - **数据优势**：积累用户行为数据，优化推荐算法
    - **品牌认知**：在目标市场建立强势品牌地位

- **4.6 SWOT分析与战略定位**
  - **4.6.1 优势（Strengths）**
    - **产品定位清晰**：专注本地信息服务，目标明确
    - **用户体验优秀**：界面简洁、操作便捷、无广告干扰
    - **技术门槛适中**：基于成熟技术栈，开发风险可控
    - **运营成本较低**：免费模式，无需复杂的交易处理
  - **4.6.2 劣势（Weaknesses）**
    - **品牌知名度低**：新产品，需要时间建立市场认知
    - **用户基数小**：初期用户获取和留存面临挑战
    - **盈利模式不明确**：短期内无明确收入来源
    - **内容冷启动困难**：需要积累足够的信息内容
  - **4.6.3 机会（Opportunities）**
    - **下沉市场空白**：三四线城市本地信息服务供给不足
    - **用户需求增长**：本地生活服务需求持续增长
    - **技术环境成熟**：小程序生态、云服务等基础设施完善
    - **政策支持**：数字化转型、乡村振兴等政策利好
  - **4.6.4 威胁（Threats）**
    - **大平台下沉**：58同城等大平台可能加强下沉市场布局
    - **模式易复制**：产品模式相对简单，容易被模仿
    - **监管风险**：信息平台面临内容审核、数据安全等监管要求
    - **用户习惯固化**：部分用户已习惯现有平台，迁移成本高
  - **4.6.5 战略定位**
    - **短期定位**：本地信息服务的轻量级替代方案
    - **中期定位**：区域性本地生活信息服务平台
    - **长期定位**：下沉市场本地生活服务的基础设施

## 5. 产品功能需求体系

- **5.1 核心功能模块概述**
  - **5.1.1 四大核心板块**：
    - **商铺板块**：本地商家信息展示与搜索
    - **信息板块**：个人信息发布与查询
    - **出行板块**：本地出行信息对接
    - **跑腿板块**：本地跑腿服务信息
  - **5.1.2 功能模块优先级**：
    - **最高优先级**：四大板块基础功能、用户注册登录、信息发布
    - **高优先级**：搜索筛选、位置服务、消息通知
    - **中优先级**：优惠券功能、用户评价、收藏关注
    - **低优先级**：社区互动、数据统计
  - **5.1.3 法律风险防控模块**：
    - **用户协议与隐私政策**：符合法律法规的用户协议和隐私政策
    - **免责声明系统**：全平台统一的免责声明体系
    - **内容审核机制**：自动+人工审核相结合的内容审核系统
    - **投诉举报处理**：完整的投诉举报受理和处理流程
    - **风险提示系统**：交易风险、信息真实性风险等提示系统
- **5.2 核心功能模块详细说明**

  - **5.2.0 首页功能模块 (Homepage Module)**
    - **功能概述**：首页作为用户进入小程序的第一入口，提供核心功能快捷访问和信息流展示。
    - **核心子模块**：
      - **5.2.0.1 扫码呼叫服务 (QR Code Call Service)**
        - **功能定位**：为用户提供免注册、免登录的快速扫码呼叫服务入口
        - **使用场景**：用户在餐厅、咖啡厅、酒店等场所扫描商家提供的服务二维码，快速呼叫服务
        - **功能特性**：
          - **免注册使用**：用户无需注册登录即可使用扫码呼叫功能
          - **一键扫码**：首页显著位置放置"扫码呼叫"按钮，调用微信小程序原生扫码API
          - **专用二维码识别**：仅识别平台生成的专用服务二维码，其他二维码显示"无效二维码"提示
          - **引流机制**：扫码后提示用户"建议使用平台扫码获得更好体验"，引导用户使用平台功能
        - **安全机制**：
          - **二维码格式验证**：扫码后验证二维码格式是否为平台专用格式
          - **防恶意替换**：通过加密token和场景ID验证，防止恶意二维码替换
          - **使用提示**：明确告知用户仅扫描可信来源的二维码
      - **5.2.0.2 信息流展示 (Information Feed)**
        - **最新发布**：展示各板块最新发布的信息
        - **热门推荐**：基于浏览量和互动量推荐热门信息
        - **本地推荐**：基于用户位置推荐附近的商家和服务
      - **5.2.0.3 快捷入口 (Quick Access)**
        - **板块导航**：商铺、出行、信息、跑腿四大板块快捷入口
        - **发布入口**：快速发布信息的入口
        - **搜索功能**：全局搜索入口

  - **5.2.1 商铺板块 (Shops Section)**
    - **功能概述**：为本地的实体店铺、网店、流动摊贩、师傅服务提供一个信息展示和推广的平台，方便用户查找和联系。
    - **用户价值**：
      - **C端用户**：快速找到本地各类商家和服务，了解服务详情、价格、位置，获取优惠信息。
      - **B端用户（商家/师傅）**：免费展示自己的店铺/服务，吸引本地客户，提升知名度。
    - **核心子模块/类型**：
      - **全部**
      - **实体**
      - **网店**
      - **摊贩**
      - **师傅**
    - **产品分类系统 (Product Category System)**：
      - **分类层级**：支持商家自定义二级分类体系
        - **一级分类**：商家可创建主要业务分类（如"服装"、"电器"、"维修服务"等）
        - **二级分类**：在一级分类下可创建细分类别（如"服装"下的"男装"、"女装"、"童装"等）
      - **分类管理功能**：
        - **创建分类**：商家可在"我的店铺管理"中自由创建、编辑分类名称
        - **分类排序**：支持拖拽调整分类显示顺序
        - **分类删除**：删除分类前需确认该分类下无商品，或提供商品转移选项
        - **默认分类**：系统提供"其他"作为默认分类，无法删除
      - **分类应用场景**：
        - **商品归类**：发布商品时可选择对应分类，便于管理和展示
        - **用户浏览**：用户可按分类筛选查看商家的不同类型商品
        - **搜索优化**：分类名称参与搜索匹配，提升商品被发现的概率
      - **分类限制**：
        - **数量限制**：每个商家最多创建20个一级分类，每个一级分类下最多10个二级分类
        - **名称规范**：分类名称长度限制为2-10个字符，不允许重复

    - **5.2.1.5 呼叫服务功能 (Call Service Feature)**
      - **功能概述**：为实体商家（如餐厅、咖啡厅、酒店等）提供二维码呼叫服务功能，顾客扫码后可直接呼叫服务员
      - **适用商家类型**：主要面向有现场服务需求的实体商家
      - **服务二维码生成与管理**：
        - **二维码生成**：商家可在"我的呼叫服务"中创建服务场景，系统自动生成专用二维码
        - **场景配置**：支持自定义服务场景名称（如"3号桌"、"包间A"等）
        - **服务内容设置**：可配置多种服务选项（如加水、点单、催菜、买单等）
        - **个性化设置**：支持自定义欢迎语、成功提示语、服务时间等
        - **铃声选择**：可选择不同的提示铃声
        - **备注开关**：可设置是否允许顾客添加备注
      - **二维码安全机制 (Anti-Photo Security)**：
        - **动态Token防护**：
          - 每个二维码包含动态生成的加密token
          - Token与场景ID、时间戳、随机数绑定
          - 服务端验证token有效性和时效性
        - **防拍照技术方案**：
          - **时效性验证**：二维码包含时间戳，超过一定时间（如24小时）自动失效
          - **使用次数限制**：可设置单个二维码的最大使用次数
          - **设备指纹验证**：记录首次扫码的设备信息，异常设备扫码时进行额外验证
          - **地理位置验证**：验证扫码位置是否在商家设定的服务范围内
          - **行为模式分析**：检测异常扫码行为（如短时间内大量扫码）
        - **防恶意替换机制**：
          - **数字签名**：二维码内容使用平台私钥进行数字签名
          - **内容完整性校验**：验证二维码内容是否被篡改
          - **商家验证**：验证二维码是否为该商家合法生成
          - **黑名单机制**：对检测到的恶意二维码进行黑名单处理
      - **呼叫流程**：
        - **扫码识别**：用户扫描二维码，系统验证二维码有效性
        - **服务选择**：显示该场景可用的服务选项，用户可多选
        - **信息确认**：用户可添加备注信息（如开启）
        - **呼叫发送**：确认后向商家发送呼叫通知
        - **状态反馈**：显示呼叫成功提示，告知预计响应时间
      - **商家接收与处理**：
        - **实时通知**：商家端实时接收呼叫通知，支持声音和震动提醒
        - **信息展示**：显示呼叫的桌号/场景、服务内容、备注信息、呼叫时间
        - **处理状态**：商家可标记呼叫处理状态（已接收、处理中、已完成）
        - **历史记录**：保存呼叫历史记录，便于商家分析服务需求
      - **用户体验优化**：
        - **免注册使用**：用户无需注册即可使用扫码呼叫功能
        - **界面简洁**：呼叫界面简洁明了，操作步骤最少化
        - **反馈机制**：支持服务完成后的评价功能
        - **多语言支持**：支持中英文界面切换（可选）

    - **通用基础信息字段 (适用于所有商铺类型)**：
      - **店铺/服务名称/昵称** (必填)
      - **头像** (必填，商家自行上传)
      - **联系电话** (必填，发布时需验证码，前端脱敏显示，点击可复制或拨打)
      - **主营业务/经营范围/服务简介** (必填，自定义文本描述)
      - **营业时间** (可选填，支持多段或每日不同时间设置，需明确具体方案)
      - **认证图标** (可选，平台根据特定条件授予，如资质认证，需明确具体条件和展示逻辑)
      - **距离显示** (自动计算用户与商家定位的直线距离)
      - **信息发布/编辑验证**：商家发布或修改任何信息（包括基础信息、商品/服务信息等）均需通过手机验证码进行验证。
## 10. 微信小程序开发特定要求

- **10.1 微信小程序平台限制与合规要求**
  - **10.1.1 平台审核要求**
    - **内容合规**：严格遵循微信小程序内容审核标准，避免违规内容
    - **功能合规**：不得包含微信明确禁止的功能（如虚拟支付、外部链接跳转等）
    - **UI合规**：界面设计必须符合微信小程序设计规范
    - **隐私合规**：必须提供符合要求的隐私政策，明确说明数据收集和使用方式
  - **10.1.2 技术限制**
    - **包体积限制**：主包不超过2MB，分包不超过2MB
    - **API限制**：仅使用微信小程序开放的API，不使用非官方或未公开API
    - **网络请求限制**：遵循微信小程序网络请求域名白名单机制
    - **本地存储限制**：遵循微信小程序存储大小限制（10MB）
  - **10.1.3 发布与更新限制**
    - **审核周期**：预留足够的审核时间（通常1-7天）
    - **更新频率**：合理规划更新频率，避免频繁提交审核
    - **版本管理**：建立完善的版本管理机制，支持回退方案

- **10.2 微信小程序特有功能集成**
  - **10.2.1 微信登录与用户信息**
    - **登录流程**：使用微信授权登录，获取用户openid和unionid
    - **用户信息**：符合新规范的用户信息获取方式（需用户主动授权）
    - **手机号获取**：使用微信官方接口获取用户手机号
  - **10.2.2 微信支付集成**
    - **支付场景**：仅用于优惠券/体验券购买，不涉及商品交易
    - **支付流程**：符合微信支付规范的完整支付流程
    - **退款机制**：支持符合规范的退款流程
  - **10.2.3 地理位置服务**
    - **位置获取**：使用微信小程序位置API获取用户位置
    - **地图展示**：集成微信小程序地图组件展示位置信息
    - **位置搜索**：基于位置的商家和服务搜索功能
  - **10.2.4 消息订阅与推送**
    - **订阅消息**：使用微信订阅消息API，替代已废弃的模板消息
    - **消息类型**：新消息提醒、审核结果通知、活动提醒等
    - **频率控制**：遵循微信订阅消息的发送频率限制
  - **10.2.5 微信扫码功能**
    - **二维码生成**：使用微信小程序码API生成平台专用二维码
    - **扫码识别**：集成微信扫码API，支持扫描平台二维码
    - **场景值应用**：利用场景值传递参数，实现扫码直达功能

- **10.3 微信小程序前端开发规范**
  - **10.3.1 文件结构与命名规范**
    - **目录结构**：按功能模块和页面组织清晰的目录结构
    - **命名规则**：使用统一的命名规范（如小驼峰、连字符等）
    - **组件化**：合理拆分和组织可复用组件
  - **10.3.2 WXML与WXSS规范**
    - **WXML规范**：语义化标签使用，避免过深嵌套
    - **WXSS规范**：使用rpx单位适配不同屏幕，避免内联样式
    - **样式复用**：建立全局样式和主题变量，提高样式复用性
  - **10.3.3 JavaScript规范**
    - **ES6标准**：使用ES6+语法，提高代码可读性和维护性
    - **异步处理**：使用Promise或async/await处理异步操作
    - **错误处理**：完善的错误捕获和处理机制
  - **10.3.4 性能优化规范**
    - **首屏加载**：优化首屏加载速度，关键内容优先展示
    - **分包加载**：合理使用分包加载，减少主包体积
    - **图片优化**：使用适当格式和大小的图片，考虑CDN加速
    - **缓存策略**：合理使用本地缓存，减少不必要的网络请求

- **10.4 微信小程序测试与上线规范**
  - **10.4.1 测试环境配置**
    - **开发者工具**：使用最新版微信开发者工具进行开发和测试
    - **真机测试**：在不同型号手机上进行真机测试
    - **体验版测试**：使用体验版进行内部测试和验收
  - **10.4.2 测试用例设计**
    - **功能测试**：覆盖所有核心功能的测试用例
    - **兼容性测试**：不同设备和系统版本的兼容性测试
    - **性能测试**：加载速度、响应时间、内存占用等性能指标测试
  - **10.4.3 上线前检查清单**
    - **合规检查**：确保内容和功能符合微信平台规范
    - **体验检查**：确保用户体验流畅，无明显bug
    - **性能检查**：确保性能指标达到要求
    - **安全检查**：确保数据传输和存储安全
  - **10.4.4 版本迭代与更新计划**
    - **版本规划**：明确的版本迭代计划和功能发布路线图
    - **灰度发布**：重大更新考虑使用灰度发布策略
    - **紧急修复**：建立紧急bug修复和版本回退机制

- **10.5 微信小程序与后端对接规范**
  - **10.5.1 API设计原则**
    - **RESTful设计**：遵循RESTful API设计规范
    - **轻量化**：API返回数据精简，减少不必要的字段
    - **版本控制**：API支持版本控制，便于后续迭代
  - **10.5.2 数据交互格式**
    - **JSON格式**：统一使用JSON格式进行数据交互
    - **状态码规范**：统一的状态码和错误信息格式
    - **数据结构**：清晰定义的数据结构和字段类型
  - **10.5.3 安全通信要求**
    - **HTTPS加密**：所有API通信必须使用HTTPS
    - **数据加密**：敏感数据传输采用额外加密措施
    - **身份验证**：统一的token认证机制
    - **防重放攻击**：请求签名和时间戳验证
  - **10.5.4 离线功能支持**
    - **本地缓存策略**：关键数据本地缓存，支持离线查看
    - **数据同步机制**：网络恢复后的数据同步策略
    - **操作队列**：离线操作缓存和网络恢复后的处理机制

## 11. 高保真UI/UX原型开发与验收

- **11.1 高保真原型开发要求**
  - **11.1.1 原型工具选择**
    - **推荐工具**：Figma、Sketch、Adobe XD等支持微信小程序设计的专业工具
    - **禁用工具**：不得使用HTML/CSS/JS等Web技术制作原型
    - **交付格式**：原型源文件(.fig/.sketch/.xd)和导出的预览图片/PDF
  - **11.1.2 原型完整性要求**
    - **页面覆盖率**：必须覆盖PRD中定义的所有必需界面
    - **流程完整性**：必须展示完整的用户操作流程和状态转换
    - **交互细节**：必须包含所有关键交互细节（点击、滑动、弹窗等）
    - **组件状态**：展示组件的不同状态（默认、点击、禁用等）
  - **11.1.3 微信小程序规范遵循**
    - **组件使用**：使用微信小程序官方组件库或符合规范的自定义组件
    - **导航模式**：符合微信小程序的导航模式和交互习惯
    - **界面尺寸**：使用标准的微信小程序界面尺寸（750rpx宽度基准）
    - **安全区域**：考虑不同机型的安全区域，特别是刘海屏和全面屏

- **11.2 原型交互与动效要求**
  - **11.2.1 基础交互规范**
    - **点击反馈**：所有可点击元素必须有明确的视觉反馈
    - **滑动手势**：定义清晰的滑动手势和效果
    - **表单交互**：完整的表单输入、验证和提交流程
    - **列表交互**：下拉刷新、上拉加载更多等列表交互
  - **11.2.2 动效设计规范**
    - **过渡动画**：页面切换、弹窗显示等过渡动画
    - **微交互**：按钮点击、开关切换等微交互动效
    - **加载动画**：统一的加载状态动画
    - **动效原则**：动效简洁流畅，不过度干扰用户，符合微信生态风格

- **11.3 原型测试与迭代**
  - **11.3.1 内部测试**
    - **可用性测试**：团队内部进行可用性测试，收集反馈
    - **一致性检查**：检查设计与PRD要求的一致性
    - **技术可行性评估**：开发团队评估设计的技术可行性
  - **11.3.2 用户测试**
    - **测试用户招募**：招募目标用户群体进行测试
    - **测试任务设计**：设计覆盖核心功能的测试任务
    - **反馈收集**：通过观察和访谈收集用户反馈
  - **11.3.3 迭代优化**
    - **问题分类**：将收集的问题按严重程度和优先级分类
    - **解决方案**：针对关键问题提出设计优化方案
    - **迭代验证**：优化后再次进行测试验证

- **11.4 原型验收标准**
  - **11.4.1 功能完整性**
    - **核心功能覆盖**：所有核心功能都有对应的界面和交互设计
    - **边界情况处理**：包含错误状态、空状态、加载状态等边界情况
    - **用户流程完整**：用户可以完成所有关键任务的完整流程
  - **11.4.2 设计规范符合度**
    - **视觉一致性**：颜色、字体、间距等视觉元素保持一致
    - **微信规范符合度**：符合微信小程序的设计规范和限制
    - **品牌一致性**：设计风格符合产品定位和品牌调性
  - **11.4.3 用户体验评估**
    - **易用性评分**：基于用户测试的易用性评分达到预期目标
    - **学习成本**：新用户学习使用产品的时间符合预期
    - **满意度评分**：用户对设计的整体满意度评分达到预期
  - **11.4.4 技术可行性确认**
    - **开发评估**：前端开发团队确认设计在技术上可实现
    - **性能评估**：评估设计实现后的性能表现是否符合要求
    - **兼容性评估**：评估设计在不同设备上的兼容性

- **11.5 设计交付物清单**
  - **11.5.1 设计源文件**
    - 完整的设计源文件（Figma/Sketch/XD）
    - 组织良好的图层结构和命名
    - 完整的组件库和样式库
  - **11.5.2 设计规范文档**
    - 色彩规范：主色、辅助色、功能色等完整色彩系统
    - 字体规范：字体家族、字号、行高、字重等规范
    - 组件规范：所有UI组件的使用规则和变体
    - 间距规范：统一的间距和对齐系统
  - **11.5.3 交互原型**
    - 可交互的高保真原型（可通过Figma/InVision等工具分享）
    - 关键流程的交互说明
    - 特殊交互和动效的说明文档
  - **11.5.4 切图资源**
    - 所有界面元素的切图资源（PNG/SVG）
    - 图标库（包括不同状态）
    - 适配不同分辨率的图片资源
  - **11.5.5 验收报告**
    - 设计符合PRD要求的对照清单
    - 用户测试结果和解决方案
    - 未解决问题和后续优化建议

## 11. 产品成功评估体系

- **10.1 关键绩效指标（KPIs）定义与目标设定**
  - **10.1.1 用户增长指标**
    - **新用户注册数**：目标每月新增1000+用户（MVP阶段）
    - **月活跃用户数（MAU）**：目标6个月内达到5000+活跃用户
    - **日活跃用户数（DAU）**：目标DAU/MAU比例达到20%以上
    - **用户留存率**：次日留存≥40%，7日留存≥25%，30日留存≥15%
    - **用户获取成本（CAC）**：通过自然增长和口碑传播，控制获客成本
  - **10.1.2 内容质量指标**
    - **信息发布数量**：目标每日新增信息50+条
    - **信息质量评分**：用户评分平均4.0分以上（5分制）
    - **信息有效性**：信息被查看/联系的转化率≥15%
    - **内容审核效率**：自动审核通过率≥80%，人工审核响应时间≤2小时
    - **违规内容比例**：违规内容占比≤2%
  - **10.1.3 用户参与指标**
    - **用户互动率**：点击、收藏、分享等互动行为占比≥30%
    - **搜索成功率**：用户搜索后进行后续操作的比例≥60%
    - **发布转化率**：浏览用户中进行信息发布的比例≥10%
    - **平均使用时长**：单次使用时长≥8分钟
    - **功能使用深度**：使用3个以上功能模块的用户占比≥40%
  - **10.1.4 技术性能指标**
    - **系统可用性**：服务可用率≥99.5%
    - **页面加载速度**：首屏加载时间≤3秒
    - **API响应时间**：平均响应时间≤500ms
    - **错误率**：系统错误率≤0.1%
    - **并发处理能力**：支持1000+并发用户访问

- **10.2 北极星指标选择与价值关联分析**
  - **10.2.1 北极星指标定义**
    - **主指标**：月活跃用户数（MAU）
    - **辅助指标**：用户月均使用频次、信息发布成功率
  - **10.2.2 价值关联分析**
    - **用户价值体现**：MAU增长代表产品解决了用户真实需求
    - **商业价值潜力**：活跃用户基数是未来商业化的基础
    - **产品健康度**：持续的MAU增长反映产品生命力
    - **市场认可度**：MAU增长速度体现市场对产品的接受程度
  - **10.2.3 指标分解与驱动因素**
    - **新用户获取**：通过口碑传播、社交分享获得新用户
    - **用户激活**：新用户完成首次信息发布或成功找到所需信息
    - **用户留存**：通过持续的价值提供保持用户活跃
    - **用户推荐**：满意用户向朋友推荐产品

- **10.3 HEART指标体系（Google用户体验评估框架）**
  - **10.3.1 Happiness（用户满意度）**
    - **测量方法**：应用内评分、用户反馈调研、NPS净推荐值
    - **目标值**：应用商店评分≥4.5分，NPS≥50
    - **关键问题**："您会向朋友推荐这个产品吗？"
    - **改进策略**：定期收集用户反馈，快速响应用户需求
  - **10.3.2 Engagement（用户参与度）**
    - **测量指标**：DAU、会话时长、页面浏览深度、功能使用频率
    - **目标值**：平均会话时长≥8分钟，页面浏览深度≥3页
    - **细分维度**：按用户类型、功能模块、时间段分析
    - **提升策略**：优化内容推荐算法，增加用户互动功能
  - **10.3.3 Adoption（功能采用率）**
    - **测量范围**：新功能使用率、核心功能渗透率、高级功能采用率
    - **目标值**：核心功能使用率≥80%，新功能7天采用率≥30%
    - **分析维度**：功能发现路径、使用障碍、用户教育效果
    - **优化方向**：改进功能引导，降低使用门槛
  - **10.3.4 Retention（用户留存率）**
    - **关键指标**：次日留存、7日留存、30日留存、90日留存
    - **目标值**：次日留存≥40%，7日留存≥25%，30日留存≥15%
    - **流失分析**：流失用户行为路径、流失原因调研
    - **留存策略**：个性化推送、用户召回、价值提醒
  - **10.3.5 Task Success（任务完成率）**
    - **核心任务**：信息搜索、信息发布、商家查找、联系建立
    - **成功定义**：用户完成预期操作并获得满意结果
    - **测量方法**：漏斗分析、用户路径追踪、任务完成时间
    - **优化重点**：简化操作流程，提高信息匹配准确性

- **10.4 商业指标与用户价值指标平衡**
  - **10.4.1 阶段性指标重点**
    - **MVP阶段（0-6个月）**：专注用户价值指标，建立用户基础
    - **成长阶段（6-18个月）**：平衡用户价值与商业探索
    - **成熟阶段（18个月+）**：商业指标与用户价值并重
  - **10.4.2 价值平衡原则**
    - **用户价值优先**：确保商业化不损害核心用户体验
    - **长期价值导向**：避免短期商业利益损害长期用户关系
    - **透明度原则**：商业化功能对用户保持透明和可选择
  - **10.4.3 潜在商业指标设计**
    - **信息推广收入**：商家信息置顶、推荐位收费
    - **增值服务收入**：认证服务、高级功能订阅
    - **广告收入**：精准本地广告投放（严格控制数量和质量）

- **10.5 数据收集与分析报告机制**
  - **10.5.1 数据埋点策略**
    - **用户行为埋点**：页面访问、按钮点击、搜索行为、停留时间
    - **业务流程埋点**：信息发布流程、商家查找流程、联系建立流程
    - **性能监控埋点**：页面加载时间、接口响应时间、错误率统计
    - **转化漏斗埋点**：关键业务路径的各个转化节点
  - **10.5.2 实时监控与预警**
    - **关键指标监控**：DAU、MAU、留存率、任务完成率实时监控
    - **异常预警机制**：指标异常波动自动预警通知
    - **性能监控**：系统性能、接口可用性、错误率监控
    - **用户反馈监控**：负面评价、投诉举报实时跟踪
  - **10.5.3 定期分析报告**
    - **周报**：核心指标趋势、异常事件分析、用户反馈汇总
    - **月报**：深度数据分析、用户行为洞察、产品优化建议
    - **季报**：产品发展评估、市场竞争分析、战略调整建议
    - **专项报告**：新功能效果评估、用户调研分析、A/B测试结果

---

## **附录A：设计师工作指导原则**

### **A.1 产品定位理解要求**
- **核心定位**：本产品是信息匹配平台，不是电商平台
- **主要价值**：帮助用户快速找到本地商家和服务信息
- **设计理念**：简洁、高效、本地化、信息导向
- **避免误区**：不要设计成购物平台、不要添加复杂的商业功能

### **A.2 技术平台限制说明**
- **开发平台**：微信小程序原生开发
- **禁用技术**：HTML、CSS、JavaScript网页技术
- **组件库**：必须使用微信小程序官方组件
- **样式规范**：使用WXSS样式语言，不是CSS

### **A.3 商家端功能设计限制**
- **严禁独立后台**：不得设计独立的商家管理系统
- **集成原则**：所有商家功能必须集成在用户个人中心
- **功能限制**：不得设计复杂的数据统计、营销工具、订单管理
- **简化原则**：商家功能保持简单、直观、易用

### **A.4 界面设计标准**
- **视觉风格**：现代简约、清新自然、符合微信生态
- **色彩搭配**：主色调建议使用微信绿或清新蓝绿色系
- **字体规范**：使用微信小程序默认字体，确保跨平台一致性
- **布局原则**：信息层次清晰、操作路径简单、减少用户认知负担

### **A.5 用户体验设计要求**
- **操作简化**：最多3步完成核心任务
- **信息优先**：突出展示关键信息，弱化装饰元素
- **本地化体验**：强调地理位置、距离、本地特色
- **快速响应**：界面反馈及时、加载状态清晰

---

## **附录B：常见设计问题与解决方案**

### **B.1 商家端设计常见错误**
- **错误**：设计独立的商家管理后台
- **正确**：在用户个人中心添加"商家模式"切换
- **错误**：设计复杂的数据分析图表
- **正确**：提供简单的信息发布和管理功能
- **错误**：设计订单管理系统
- **正确**：设计信息发布和联系方式管理

### **B.2 平台定位设计常见错误**
- **错误**：设计购物车、支付流程
- **正确**：设计信息展示和联系建立
- **错误**：设计商品详情页
- **正确**：设计商家信息详情页
- **错误**：设计交易评价系统
- **正确**：设计服务体验评价系统

### **B.3 技术实现设计常见错误**
- **错误**：使用HTML文件格式
- **正确**：使用设计工具原生格式（.fig、.sketch等）
- **错误**：设计复杂的CSS动画
- **正确**：使用微信小程序支持的简单动画
- **错误**：设计不符合小程序规范的交互
- **正确**：遵循微信小程序交互规范

### **B.4 设计交付常见问题**
- **问题**：设计稿不完整，缺少关键界面
- **解决**：严格按照PRD界面清单检查设计完整性
- **问题**：设计风格不统一
- **解决**：建立设计规范文档，确保一致性
- **问题**：设计不符合微信小程序规范
- **解决**：深入学习微信小程序设计指南

---

## **附录C：PRD文档维护说明**

### **C.1 文档版本管理**
- **版本号规则**：主版本.次版本.修订版本（如1.2.3）
- **更新频率**：根据产品迭代需求和市场反馈及时更新
- **变更记录**：详细记录每次修改的内容、原因、影响范围
- **审批流程**：重大变更需要产品、技术、设计团队共同评审

### **C.2 文档使用指南**
- **阅读顺序**：建议按章节顺序完整阅读，理解产品全貌
- **重点关注**：各角色重点关注与自己工作相关的章节
- **疑问处理**：对PRD内容有疑问时，及时与产品经理沟通确认
- **反馈机制**：发现问题或改进建议，通过正式渠道反馈

### **C.3 跨团队协作**
- **设计团队**：重点关注第6章用户体验设计和附录A设计指导
- **开发团队**：重点关注第7-8章技术架构和功能需求
- **测试团队**：重点关注第9章质量保证和验收标准
- **运营团队**：重点关注第10章产品评估指标和商业模式

---

## **附录D：界面交互优化更新记录**

### **D.1 计费方式动态化优化 (2024年12月更新)**
#### **优化内容**
- **问题描述**：`publish-transportation.html`页面中计费方式选项固定，未根据出行类型动态调整
- **解决方案**：
  - 人找车：显示"面议"、"每人"选项
  - 车找人：显示"面议"、"空位"选项  
  - 货找车：显示"面议"、"运费"选项
  - 车找货：显示"面议"、"每趟"选项
  - 代驾：显示"面议"、"每公里"选项
  - 默认选中"面议"选项
- **技术实现**：添加`updatePriceTypeOptions`函数，根据出行类型动态更新计费方式选项

### **D.2 四大板块导航交互修复 (2024年12月更新)**
#### **修复内容**
- **问题描述**：四大板块（商铺、信息、出行、跑腿）导航按钮点击无响应
- **解决方案**：为所有板块导航按钮添加`onclick`事件，实现页面跳转功能
- **涉及页面**：
  - `errands.html`：添加商铺、信息、出行页面跳转
  - `information.html`：添加商铺、出行、跑腿页面跳转
  - `transportation.html`：添加商铺、信息、跑腿页面跳转
  - `shops.html`：添加信息、出行、跑腿页面跳转

### **D.3 分类筛选交互优化 (2024年12月更新)**
#### **优化内容**
- **问题描述**：各板块分类筛选按钮点击无响应，颜色不统一
- **解决方案**：
  - **跑腿板块**：使用teal色系（`bg-teal-600`）作为选中状态
  - **信息板块**：使用紫色系（`bg-purple-600`）作为选中状态
  - **出行板块**：使用橙色系（`bg-orange-600`）作为选中状态
  - **商铺板块**：使用蓝色系（`bg-blue-600`）作为选中状态
- **交互逻辑**：点击分类按钮时，重置所有按钮状态，设置当前按钮为选中状态

### **D.4 表单控件交互修复 (2024年12月更新)**
#### **修复内容**
- **问题描述**：`publish-transportation.html`页面中线路类型和服务范围选项点击无响应
- **解决方案**：为`label`元素添加点击事件监听器，确保点击时能正确触发内部`radio`按钮的`change`事件
- **技术实现**：使用`addEventListener`为每个`label`添加点击事件处理

## **附录E：出行板块实现更新记录**

### **D.1 出行卡片类型实现状态 (2024年12月更新)**

**已实现的5种出行类型卡片**：

1. **人找车卡片**
   - **标签样式**：蓝色渐变背景 (from-blue-500 to-blue-600)
   - **核心信息**：起点终点、出发时间、人数同行、途径信息、价格(每人)、联系方式
   - **特色功能**：途径站点显示、人数统计、发布时间展示

2. **车找人卡片**
   - **标签样式**：绿色渐变背景 (from-green-500 to-green-600)
   - **核心信息**：起点终点、出发时间、车辆信息、可载人数、价格、联系方式
   - **特色功能**：车辆类型展示、剩余座位数、路线固定性标识

3. **货找车卡片**
   - **标签样式**：橙色渐变背景 (from-orange-500 to-orange-600)
   - **核心信息**：起点终点、货物信息、重量体积、装卸要求、价格、联系方式
   - **特色功能**：货物类型分类、重量规格、特殊要求标注

4. **车找货卡片**
   - **标签样式**：紫色渐变背景 (from-purple-500 to-purple-600)
   - **核心信息**：服务路线、车辆类型、载重能力、服务范围、价格、联系方式
   - **特色功能**：车辆规格展示、载重限制、服务区域标识

5. **代驾服务卡片**
   - **标签样式**：青色渐变背景 (from-cyan-500 to-cyan-600)
   - **核心信息**：服务区域、驾龄经验、服务时间、价格标准、联系方式
   - **特色功能**：经验年限展示、服务时段、价格计费方式

### **D.2 界面设计特点**

- **统一视觉风格**：所有卡片采用相同的布局结构和间距规范
- **颜色区分系统**：不同类型使用不同的渐变色彩进行视觉区分
- **信息层次清晰**：标题、价格、时间、联系方式等信息按重要性分层展示
- **交互友好**：电话按钮、途径信息等关键操作元素突出显示
- **响应式设计**：适配移动端显示，确保在不同屏幕尺寸下的良好体验

### **D.3 出行卡片优化记录 (2024年12月更新)**

#### **优化记录1：信息精简优化**
- **优化时间**：2024年12月
- **优化内容**：
  - 删除"人找车"卡片中的"非固定线路"标签和"途径"信息
  - 删除"货找车"卡片中的"途径"信息
- **优化理由**：
  - 人找车通常是点对点直达，途径信息冗余
  - 货找车通常是直达配送，简化信息提升用户体验
- **效果**：界面更简洁明了，用户关注重点更突出

#### **优化记录2：认证与联系方式标准化**
- **优化时间**：2024年12月
- **优化内容**：
  - **认证标签统一**：为所有卡片添加"已认证"或"未认证"状态标签
  - **联系方式标准化**：将所有"微信"标签统一改为"电话"标签
  - **线路类型补充**：为"人找车"卡片重新添加"非固定线路"标签
  - **状态信息增强**：为"代驾服务"卡片添加"距离"和"在线"状态标签
  - **服务信息优化**：
    - 删除"24小时服务"标签，改为具体"服务时间：8:00-24:00"
    - 将"服务范围"统一规范为"城区内"或"全县城"
- **认证状态分配**：
  - 人找车：已认证
  - 车找人：已认证  
  - 货找车：未认证
  - 车找货：已认证
  - 代驾服务：已认证
- **在线状态逻辑**：代驾服务根据设定的服务时间(8:00-24:00)显示在线/离线状态
- **优化理由**：
  - 提升用户信任度和安全感
  - 统一联系方式，便于用户理解和使用
  - 增加实用的距离和状态信息
  - 明确服务时间和范围，避免误解
- **效果**：用户体验更加统一和专业，信息更加准确实用

---

**文档结束**

*本PRD文档是产品开发的核心指导文件，所有团队成员必须严格遵循文档要求，确保产品按照既定方向高质量交付。*
