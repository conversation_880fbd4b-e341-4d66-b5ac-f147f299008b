# 产品需求文档 (Product Requirements Document)

## 1. 文档信息管理

- **1.1 版本历史记录**
  | 版本号 | 修改日期   | 主要变更内容 | 责任人 | 审批状态 |
  | ------ | ---------- | ------------ | ------ | -------- |
  | V1.0   | 2024-01-15 | 初稿创建     | 产品经理 | 已审批 |
  | V1.1   | 2024-01-20 | 完善技术规范和设计标准 | 产品经理 | 待审批 |
- **1.2 文档编写目的与适用范围**
  - **目的**：本文档旨在明确“本地助手”小程序的产品需求，作为产品设计、开发、测试和运营的依据。
  - **范围**：适用于“本地助手”小程序的所有相关方，包括产品、设计、研发、测试及项目管理团队。
- **1.3 相关文档交叉引用与依赖关系**
  - 用户故事地图: `User_Story_Map.md` (待创建)
  - 产品路线图: `Roadmap.md` (待创建)
  - 产品评估指标框架: `Metrics_Framework.md` (待创建)
- **1.4 文档审批流程与签字确认**
  - **审批层级**：
    - **初审**：产品经理自审（功能完整性、逻辑一致性）
    - **技术审查**：技术负责人审查（技术可行性、架构合理性）
    - **设计审查**：设计负责人审查（设计规范、用户体验）
    - **最终审批**：项目负责人最终审批
  - **审批标准**：
    - 功能需求描述清晰完整，无歧义表述
    - 技术架构设计合理，符合平台限制
    - 验收标准明确，可量化可执行
    - 时间节点合理，资源配置可行
  - **变更管理**：
    - 重大变更（影响核心功能或架构）需重新走完整审批流程
    - 一般变更（细节优化）需相关负责人确认
    - 所有变更必须更新版本记录并通知相关团队

## 2. 产品战略概述

- **2.1 产品名称、定位与核心价值主张**
  - **产品名称**：本地助手
  - **产品定位**：一个专注于本地生活服务的免费信息发布与查询平台，连接本地用户与商家/服务提供者。
  - **平台性质明确定义**：
    - **信息撮合平台**：仅提供信息展示和联系方式，不参与交易流程
    - **非电商平台**：不提供在线支付、订单管理、物流跟踪等电商功能
    - **非SaaS平台**：不为商家提供复杂的店铺管理、数据分析等企业级服务
    - **轻量化工具**：专注于信息发布、搜索、联系等核心功能
  - **核心价值主张**：为用户提供便捷、高效、免费的本地生活信息获取与发布渠道，促进本地信息流通与互助。
- **2.2 产品愿景、使命与长期发展目标**
  - **愿景**：成为本地居民首选的生活服务信息平台。
  - **使命**：让本地生活更简单、更便捷。
  - **长期目标**：覆盖更广泛的本地服务领域，提升用户活跃度和信息匹配效率。
- **2.3 独特销售主张（USP）与核心竞争优势**
  - **USP**：免费、纯粹的本地信息互通平台，无中间商赚差价，信息真实可靠。
  - **核心竞争优势**：信息聚合度高、操作简便、专注于本地化服务、用户驱动内容。
- **2.4 目标平台列表**
  - **主要平台**：微信小程序（必须严格遵循微信小程序开发规范）
  - **平台限制说明**：
    - **仅支持微信小程序平台**：不得设计为Web应用或其他平台
    - **必须使用小程序技术栈**：WXML、WXSS、JavaScript，禁止使用HTML、CSS等Web技术
    - **遵循微信设计规范**：界面设计必须符合微信小程序设计指南
    - **功能限制**：受微信小程序平台政策和技术限制约束
    - **扫码功能集成**：集成微信小程序原生扫码API，支持扫描平台专用二维码
  - **严禁平台变更**：未经产品经理明确书面同意，不得将产品设计为其他平台应用
- **2.5 产品核心假设与验证策略**
  - **核心假设1**：用户对免费的本地生活信息平台有需求。
    - **验证策略**：通过MVP版本上线后的用户增长、活跃度及信息发布量进行验证。
  - **核心假设2**：用户愿意通过平台发布和获取各类本地服务信息。
    - **验证策略**：监测各板块信息发布和浏览比例，收集用户反馈。
- **2.6 商业模式概述与盈利模式设计**
  - **商业模式**：永久免费的本地信息撮合平台，不支持在线交易，仅提供线上信息展示和线下交易撮合服务。
  - **盈利模式**：
    - **优惠券/体验券收费**：用户自行扫码付款开通，40元/月、200元/半年、300元/年
    - **广告收入**：在内容卡片中穿插广告位，支持外部链接或纯展示广告
    - **后台管理授权服务**：为特定用户提供后台管理权限，可选择授权内容范围
  - **交易模式**：纯线上撮合，线下洽谈成交，平台不参与资金流转，确保零风险运营
- **2.7 产品生命周期规划与退出策略**
  - **2.7.1 产品生命周期阶段规划**
    - **孵化期（0-3个月）**：MVP开发、核心功能验证、初始用户获取
    - **成长期（3-12个月）**：功能完善、用户规模扩大、商业模式验证
    - **成熟期（12-24个月）**：市场稳定、盈利模式成熟、功能优化迭代
    - **衰退期/转型期（24个月+）**：市场饱和应对、产品转型或升级
  - **2.7.2 各阶段关键目标**
    - **孵化期目标**：DAU达到500+，核心功能使用率>60%
    - **成长期目标**：MAU达到5000+，月收入达到10万+
    - **成熟期目标**：MAU达到2万+，实现盈亏平衡
  - **2.7.3 风险预警与退出策略**
    - **市场风险**：竞品冲击、政策变化、用户需求转移
    - **技术风险**：平台政策变更、技术架构过时
    - **退出条件**：连续6个月MAU下降超过30%，或无法实现盈亏平衡
    - **退出方案**：用户数据导出、服务平稳过渡、合规关停流程

## 3. 深度用户研究

- **3.1 目标用户画像构建**
  - **3.1.1 人口统计学特征**
    - **年龄分布**：18-60岁，核心用户群体20-50岁（占比65%）
    - **性别分布**：男性45%，女性55%，女性在信息发布和商家查询方面更活跃
    - **收入水平**：月收入3000-8000元，以中等收入群体为主
    - **教育背景**：高中及以上学历占比80%，具备基本的移动互联网使用能力
    - **地域分布**：县域市场，城镇化率60-80%的区域,城区人口约60万人，总人口约160万人
  - **3.1.2 心理特征与价值观分析**
    - **实用主义导向**：注重产品的实际价值，对复杂功能不感兴趣
    - **本地归属感强**：对本地社区有强烈认同感，信任本地推荐
    - **价格敏感度高**：对免费服务有偏好，付费意愿相对较低
    - **社交需求明显**：希望通过平台建立本地社交关系
    - **安全意识增强**：对个人信息保护和交易安全要求提高
  - **3.1.3 行为习惯与使用偏好**
    - **使用时间**：晚上19:00-22:00为高峰期，周末使用频率更高
    - **使用场景**：主要在家中、工作间隙、通勤路上使用
    - **操作习惯**：偏好简单直观的界面，不喜欢复杂的操作流程
    - **信息获取方式**：习惯通过搜索和分类浏览获取信息
    - **分享行为**：乐于分享有价值的本地信息给朋友
  - **3.1.4 核心需求痛点与期望价值**
    - **信息获取痛点**：现有平台信息杂乱、广告多、搜索不精准
    - **信息发布痛点**：发布流程复杂、审核时间长、曝光度低
    - **信任建立痛点**：难以判断信息真实性，缺乏有效的信用机制
    - **期望价值**：简单易用、信息真实、本地化精准、完全免费
  - **3.1.5 用户动机分析与决策影响因素**
    - **主要动机**：解决实际生活问题、获取优惠信息、扩展社交圈
    - **决策因素**：信息质量、平台口碑、使用便捷性、安全保障
    - **转换成本**：学习成本、数据迁移成本、社交关系重建成本
  - **3.1.6 C端用户（信息获取者/服务需求方/信息发布者）**
    - **人口统计学特征**：本地常住居民，年龄段覆盖较广，对移动互联网使用熟练。
    - **心理特征与价值观**：追求便捷、高效、高性价比的生活服务，乐于分享和获取本地信息。
    - **行为习惯**：习惯通过线上渠道获取信息，对本地新闻、优惠、服务有较高关注度。
    - **核心需求痛点**：传统信息渠道分散、效率低；部分服务寻找困难；希望有更直接的本地信息交互方式。
    - **用户动机**：解决生活中的实际需求（如找工作、租房、购物、出行、求助等）。
  - **3.1.7 B端用户/服务提供方（商家/师傅/个人服务者/信息发布者）**
    - **特征**：本地实体店主、网店经营者、个体工商户、手艺人、有闲置资源或技能的个人。
    - **核心需求痛点**：获客渠道有限、推广成本高、希望有更直接的方式触达本地潜在客户。
    - **用户动机**：扩大服务范围、增加客源、提升知名度、发布闲置信息。

- **3.2 用户场景深度分析**
  - **3.2.1 核心使用场景详细描述**
    - **场景一：寻找本地商家**
      - **时间**：工作日午休时间、周末购物时间
      - **地点**：办公室、家中、商圈
      - **环境**：需要快速找到附近的餐厅、超市、服务店
      - **情境**："我想找个离公司近的理发店"
      - **用户期望**：快速定位、查看评价、获取联系方式
    - **场景二：发布闲置物品**
      - **时间**：晚上空闲时间、周末整理时间
      - **地点**：家中
      - **环境**：整理物品时发现闲置品
      - **情境**："家里有台用不到的小家电想卖掉"
      - **用户期望**：简单发布、快速成交、安全交易
    - **场景三：寻找跑腿服务**
      - **时间**：工作日白天、紧急情况
      - **地点**：办公室、家中
      - **环境**：无法亲自办事的紧急情况
      - **情境**："临时有事需要有人帮忙取个快递"
      - **用户期望**：快速响应、价格合理、服务可靠
  - **3.2.2 边缘使用场景与异常情况考量**
    - **深夜紧急求助**：医院陪护、紧急用车等特殊需求
    - **节假日服务需求**：春节期间的特殊服务需求
    - **恶劣天气影响**：台风、暴雨等天气对服务的影响
    - **网络环境差**：偏远地区网络不稳定的使用场景
  - **3.2.3 跨平台使用场景与切换需求**
    - **移动端主导**：日常浏览、信息发布、即时沟通
    - **PC端辅助**：商家管理、批量操作、数据分析
    - **平台间切换**：从微信分享进入、向朋友圈分享信息

- **3.3 用户调研数据洞察**
  - **3.3.1 用户满意度调研**
    - **现有平台满意度**：58同城满意度65%，本地论坛满意度78%
    - **主要不满因素**：广告过多（85%）、界面复杂（72%）、信息不准确（68%）
    - **最看重功能**：搜索准确性（92%）、信息真实性（89%）、操作简便性（84%）
  - **3.3.2 使用行为数据**
    - **平均使用时长**：单次15-25分钟，每周使用3-5次
    - **功能使用频率**：搜索功能使用率95%，发布功能使用率35%
    - **信息浏览偏好**：商家信息（45%）、二手交易（25%）、招聘信息（20%）
  - **3.3.3 付费意愿调研**
    - **付费意愿**：仅有15%用户愿意为信息服务付费
    - **可接受费用**：月费用不超过10元的占比80%
    - **付费场景**：紧急服务（60%）、高质量信息（40%）、去广告（35%）

- **3.4 用户反馈收集与分析机制设计**
  - **3.4.1 反馈收集渠道**
    - **应用内反馈**：意见建议入口、问题举报功能、满意度评价
    - **社交媒体监控**：微信群、QQ群、公众号
    - **客服渠道**：在线客服、电话客服、邮件反馈
    - **用户访谈**：定期组织重点用户深度访谈
  - **3.4.2 反馈分析框架**
    - **分类标准**：功能建议、Bug反馈、体验问题、内容质量
    - **优先级评估**：影响用户数量、问题严重程度、解决难度
    - **响应机制**：24小时内确认、7天内给出解决方案
  - **3.4.3 持续改进机制**
    - **月度用户调研**：通过问卷和访谈了解用户需求变化
    - **数据驱动优化**：基于用户行为数据优化产品功能
    - **A/B测试验证**：新功能上线前进行小范围测试
    - **用户共创**：邀请活跃用户参与产品设计讨论

## 4. 市场环境与竞争分析

- **4.1 目标市场规模评估与增长趋势预测**
  - **4.1.1 市场规模分析**
    - **本地生活服务市场**：中国本地生活服务市场规模超过2亿元，年增长率保持在15%以上
    - **分类信息市场**：细分市场规模约500亿元，其中本地化信息服务占比约30%
    - **目标用户群体**：三四线城市及县域市场，潜在用户规模约3-5亿人
  - **4.1.2 增长趋势预测**
    - 下沉市场数字化程度快速提升，年增长率预计25%+
    - 本地化、个性化服务需求持续增长
    - 小程序生态日趋成熟，用户接受度不断提高
  - **4.1.3 市场机会窗口**
    - 传统分类信息平台在下沉市场覆盖不足
    - 用户对简单、免费、纯粹的信息平台有强烈需求
    - 疫情加速了本地生活服务的线上化进程

- **4.2 行业发展趋势与技术演进分析**
  - **4.2.1 行业发展趋势**
    - **本地化服务精细化**：从粗放式信息发布向精准匹配发展
    - **社区化运营**：用户更倾向于信任本地社区推荐
    - **移动优先**：移动端成为本地生活服务的主要入口
    - **内容可信度要求提升**：用户对信息真实性要求越来越高
  - **4.2.2 技术演进分析**
    - **AI技术应用**：智能推荐、资质审核、用户画像分析
    - **LBS技术成熟**：精准定位、距离计算、路径规划
    - **小程序生态完善**：开发成本降低、用户体验提升
    - **云服务普及**：降低了中小平台的技术门槛
  - **4.2.3 用户行为变化**
    - 从被动浏览向主动搜索转变
    - 对即时性、便捷性要求更高
    - 更注重隐私保护和信息安全

- **4.3 竞争格局深度剖析**
  - **4.3.1 直接竞争对手分析**
    - **58同城/赶集网**
      - 优势：品牌知名度高、信息量大、覆盖面广
      - 劣势：界面复杂、广告多、用户体验差、下沉市场渗透不足
    - **本地论坛/贴吧**
      - 优势：用户粘性强、社区氛围好
      - 劣势：信息组织混乱、搜索功能弱、移动端体验差
    - **微信群/QQ群**
      - 优势：用户信任度高、传播速度快
      - 劣势：信息易丢失、无法有效检索、管理困难
  - **4.3.2 间接竞争对手分析**
    - **美团/大众点评**：主要专注餐饮娱乐，与我们的商铺板块有部分重叠
    - **闲鱼**：二手交易平台，与我们的信息板块闲置分类竞争
    - **BOSS直聘/前程无忧**：招聘平台，与我们的招聘分类竞争
  - **4.3.3 竞争态势评估**
    - 大平台在下沉市场存在服务空白
    - 本地化、垂直化平台有发展机会
    - 用户对简洁、免费平台有需求

- **4.4 竞品功能特性对比矩阵**
  
  | 功能特性 | 本地助手 | 58同城 | 本地论坛 | 微信群 |
  |---------|---------|--------|---------|--------|
  | **界面简洁度** | ★★★★★ | ★★☆☆☆ | ★★★☆☆ | ★★★★☆ |
  | **信息分类** | ★★★★★ | ★★★★☆ | ★★☆☆☆ | ★☆☆☆☆ |
  | **搜索功能** | ★★★★☆ | ★★★★☆ | ★★☆☆☆ | ★☆☆☆☆ |
  | **本地化程度** | ★★★★★ | ★★★☆☆ | ★★★★★ | ★★★★★ |
  | **用户信任度** | ★★★☆☆ | ★★★☆☆ | ★★★★☆ | ★★★★★ |
  | **信息时效性** | ★★★★☆ | ★★★☆☆ | ★★☆☆☆ | ★★★★☆ |
  | **移动端体验** | ★★★★★ | ★★★☆☆ | ★★☆☆☆ | ★★★★☆ |
  | **免费程度** | ★★★★★ | ★★☆☆☆ | ★★★★★ | ★★★★★ |

- **4.5 市场差异化策略与竞争壁垒构建**
  - **4.5.1 差异化策略**
    - **产品差异化**：专注免费、纯粹的信息撮合，不做交易闭环
    - **用户体验差异化**：界面简洁、操作便捷、无广告干扰
    - **服务差异化**：深度本地化运营、快速响应用户需求
    - **技术差异化**：基于小程序的轻量级解决方案
  - **4.5.2 竞争壁垒构建**
    - **用户壁垒**：通过优质服务建立用户忠诚度和口碑传播
    - **内容壁垒**：积累高质量的本地信息内容库
    - **技术壁垒**：持续优化算法和用户体验
    - **运营壁垒**：建立高效的内容审核和社区管理机制
  - **4.5.3 护城河建设**
    - **网络效应**：用户和信息越多，平台价值越大
    - **数据优势**：积累用户行为数据，优化推荐算法
    - **品牌认知**：在目标市场建立强势品牌地位

- **4.6 SWOT分析与战略定位**
  - **4.6.1 优势（Strengths）**
    - **产品定位清晰**：专注本地信息服务，目标明确
    - **用户体验优秀**：界面简洁、操作便捷、无广告干扰
    - **技术门槛适中**：基于成熟技术栈，开发风险可控
    - **运营成本较低**：免费模式，无需复杂的交易处理
  - **4.6.2 劣势（Weaknesses）**
    - **品牌知名度低**：新产品，需要时间建立市场认知
    - **用户基数小**：初期用户获取和留存面临挑战
    - **盈利模式不明确**：短期内无明确收入来源
    - **内容冷启动困难**：需要积累足够的信息内容
  - **4.6.3 机会（Opportunities）**
    - **下沉市场空白**：三四线城市本地信息服务供给不足
    - **用户需求增长**：本地生活服务需求持续增长
    - **技术环境成熟**：小程序生态、云服务等基础设施完善
    - **政策支持**：数字化转型、乡村振兴等政策利好
  - **4.6.4 威胁（Threats）**
    - **大平台下沉**：58同城等大平台可能加强下沉市场布局
    - **模式易复制**：产品模式相对简单，容易被模仿
    - **监管风险**：信息平台面临内容审核、数据安全等监管要求
    - **用户习惯固化**：部分用户已习惯现有平台，迁移成本高
  - **4.6.5 战略定位**
    - **短期定位**：本地信息服务的轻量级替代方案
    - **中期定位**：区域性本地生活信息服务平台
    - **长期定位**：下沉市场本地生活服务的基础设施

## 5. 产品功能需求体系

- **5.1 整体功能架构与模块化设计**
  ```mermaid
graph TD
    A[本地助手小程序] --> B{用户端};
    A --> C{管理后台};

    B --> B1[首页];
    B --> B2[商铺板块];
    B --> B3[出行板块];
    B --> B4[信息板块];
    B --> B5[跑腿板块];
    B --> B6[发布功能];
    B --> B7[消息中心];
    B --> B8[个人中心];

    B2 --> B2_1[全部];
    B2 --> B2_1[实体];
    B2 --> B2_2[网店];
    B2 --> B2_3[摊贩];
    B2 --> B2_4[师傅];

    B3 --> B3_1[全部]
    B3 --> B3_1[车找人];
    B3 --> B3_2[人找车];
    B3 --> B3_3[货找车];
    B3 --> B3_4[车找货];
    B3 --> B3_5[代驾服务];

    B4 --> B4_1[全部];
    B4 --> B4_2[招聘];
    B4 --> B4_3[出租];
    B4 --> B4_4[求租];
    B4 --> B4_5[出售];
    B4 --> B4_6[闲置];
    B4 --> B4_7[求购];
    B4 --> B4_8[求职];
    B4 --> B4_9[其他];

    B5 --> B5_1[全部];
    B5 --> B5_2[代买];
    B5 --> B5_3[代送];
    B5 --> B5_4[代办];
    B5 --> B5_5[其他跑腿];

    B8 --> B8_1[我的发布];
    
    B8 --> B8_3[认证管理];
    B8 --> B8_4[我的优惠券/体验券(针对商家)];
    B8 --> B8_5[我的呼叫服务];
    B8 --> B8_6[设置];
    B8 --> B8_7[帮助与反馈];
    B8 --> B8_8[服务条款与隐私政策];

    B1 --> B1_1[扫码呼叫服务];
    B1 --> B1_2[信息流展示];
    B1 --> B1_3[快捷入口];
    B1 --> B1_4[本地推荐];

    C --> C1[用户管理];
    C --> C2[信息审核];
    C --> C3[举报处理];
    C --> C4[数据统计];
    C --> C5[系统设置];
  ```
- **5.2 核心功能模块详细说明**

  - **5.2.0 首页功能模块 (Homepage Module)**
    - **功能概述**：首页作为用户进入小程序的第一入口，提供核心功能快捷访问和信息流展示。
    - **核心子模块**：
      - **5.2.0.1 扫码呼叫服务 (QR Code Call Service)**
        - **功能定位**：为用户提供免注册、免登录的快速扫码呼叫服务入口
        - **使用场景**：用户在餐厅、咖啡厅、酒店等场所扫描商家提供的服务二维码，快速呼叫服务
        - **功能特性**：
          - **免注册使用**：用户无需注册登录即可使用扫码呼叫功能
          - **一键扫码**：首页显著位置放置"扫码呼叫"按钮，调用微信小程序原生扫码API
          - **专用二维码识别**：仅识别平台生成的专用服务二维码，其他二维码显示"无效二维码"提示
          - **引流机制**：扫码后提示用户"建议使用平台扫码获得更好体验"，引导用户使用平台功能
        - **安全机制**：
          - **二维码格式验证**：扫码后验证二维码格式是否为平台专用格式
          - **防恶意替换**：通过加密token和场景ID验证，防止恶意二维码替换
          - **使用提示**：明确告知用户仅扫描可信来源的二维码
      - **5.2.0.2 信息流展示 (Information Feed)**
        - **最新发布**：展示各板块最新发布的信息
        - **热门推荐**：基于浏览量和互动量推荐热门信息
        - **本地推荐**：基于用户位置推荐附近的商家和服务
      - **5.2.0.3 快捷入口 (Quick Access)**
        - **板块导航**：商铺、出行、信息、跑腿四大板块快捷入口
        - **发布入口**：快速发布信息的入口
        - **搜索功能**：全局搜索入口

  - **5.2.1 商铺板块 (Shops Section)**
    - **功能概述**：为本地的实体店铺、网店、流动摊贩、师傅服务提供一个信息展示和推广的平台，方便用户查找和联系。
    - **用户价值**：
      - **C端用户**：快速找到本地各类商家和服务，了解服务详情、价格、位置，获取优惠信息。
      - **B端用户（商家/师傅）**：免费展示自己的店铺/服务，吸引本地客户，提升知名度。
    - **核心子模块/类型**：
      - **全部**
      - 实体
      - 网店
      - 摊贩
      - 师傅
    - **产品分类系统 (Product Category System)**：
      - **分类层级**：支持商家自定义二级分类体系
        - **一级分类**：商家可创建主要业务分类（如"服装"、"电器"、"维修服务"等）
        - **二级分类**：在一级分类下可创建细分类别（如"服装"下的"男装"、"女装"、"童装"等）
      - **分类管理功能**：
        - **创建分类**：商家可在"我的店铺管理"中自由创建、编辑分类名称
        - **分类排序**：支持拖拽调整分类显示顺序
        - **分类删除**：删除分类前需确认该分类下无商品，或提供商品转移选项
        - **默认分类**：系统提供"其他"作为默认分类，无法删除
      - **分类应用场景**：
        - **商品归类**：发布商品时可选择对应分类，便于管理和展示
        - **用户浏览**：用户可按分类筛选查看商家的不同类型商品
        - **搜索优化**：分类名称参与搜索匹配，提升商品被发现的概率
      - **分类限制**：
        - **数量限制**：每个商家最多创建20个一级分类，每个一级分类下最多10个二级分类
        - **名称规范**：分类名称长度限制为2-10个字符，不允许重复

    - **5.2.1.5 呼叫服务功能 (Call Service Feature)**
      - **功能概述**：为实体商家（如餐厅、咖啡厅、酒店等）提供二维码呼叫服务功能，顾客扫码后可直接呼叫服务员
      - **适用商家类型**：主要面向有现场服务需求的实体商家
      - **服务二维码生成与管理**：
        - **二维码生成**：商家可在"我的呼叫服务"中创建服务场景，系统自动生成专用二维码
        - **场景配置**：支持自定义服务场景名称（如"3号桌"、"包间A"等）
        - **服务内容设置**：可配置多种服务选项（如加水、点单、催菜、买单等）
        - **个性化设置**：支持自定义欢迎语、成功提示语、服务时间等
        - **铃声选择**：可选择不同的提示铃声
        - **备注开关**：可设置是否允许顾客添加备注
      - **二维码安全机制 (Anti-Photo Security)**：
        - **动态Token防护**：
          - 每个二维码包含动态生成的加密token
          - Token与场景ID、时间戳、随机数绑定
          - 服务端验证token有效性和时效性
        - **防拍照技术方案**：
          - **时效性验证**：二维码包含时间戳，超过一定时间（如24小时）自动失效
          - **使用次数限制**：可设置单个二维码的最大使用次数
          - **设备指纹验证**：记录首次扫码的设备信息，异常设备扫码时进行额外验证
          - **地理位置验证**：验证扫码位置是否在商家设定的服务范围内
          - **行为模式分析**：检测异常扫码行为（如短时间内大量扫码）
        - **防恶意替换机制**：
          - **数字签名**：二维码内容使用平台私钥进行数字签名
          - **内容完整性校验**：验证二维码内容是否被篡改
          - **商家验证**：验证二维码是否为该商家合法生成
          - **黑名单机制**：对检测到的恶意二维码进行黑名单处理
      - **呼叫流程**：
        - **扫码识别**：用户扫描二维码，系统验证二维码有效性
        - **服务选择**：显示该场景可用的服务选项，用户可多选
        - **信息确认**：用户可添加备注信息（如开启）
        - **呼叫发送**：确认后向商家发送呼叫通知
        - **状态反馈**：显示呼叫成功提示，告知预计响应时间
      - **商家接收与处理**：
        - **实时通知**：商家端实时接收呼叫通知，支持声音和震动提醒
        - **信息展示**：显示呼叫的桌号/场景、服务内容、备注信息、呼叫时间
        - **处理状态**：商家可标记呼叫处理状态（已接收、处理中、已完成）
        - **历史记录**：保存呼叫历史记录，便于商家分析服务需求
      - **用户体验优化**：
        - **免注册使用**：用户无需注册即可使用扫码呼叫功能
        - **界面简洁**：呼叫界面简洁明了，操作步骤最少化
        - **反馈机制**：支持服务完成后的评价功能
        - **多语言支持**：支持中英文界面切换（可选）

    - **通用基础信息字段 (适用于所有商铺类型)**：
      - **店铺/服务名称/昵称** (必填)
      - **头像** (必填，商家自行上传)
      - **联系电话** (必填，发布时需验证码，前端脱敏显示，点击可复制或拨打)
      - **主营业务/经营范围/服务简介** (必填，自定义文本描述)
      - **营业时间** (可选填，支持多段或每日不同时间设置，需明确具体方案)
      - **认证图标** (可选，平台根据特定条件授予，如资质认证，需明确具体条件和展示逻辑)
      - **距离显示** (自动计算用户与商家定位的直线距离)
      - **信息发布/编辑验证**：商家发布或修改任何信息（包括基础信息、商品/服务信息等）均需通过手机验证码进行验证。
      - **新商铺创建流程优化**：新用户成功创建商铺后，系统必须自动跳转至 `my-shop-products.html` 页面，引导商家立即开始添加和管理商品，确保用户体验的连贯性。
    - **商品/服务展示与管理 (适用于所有商铺类型，师傅服务则为案例展示)**：
      - **功能范围限制说明**：
        - **仅支持基础信息展示**：商家只能发布和展示基础的商品/服务信息
        - **禁止复杂管理功能**：不提供库存管理、销售统计、订单管理等电商功能
        - **禁止数据分析功能**：不提供浏览量统计、转化率分析、用户画像等数据功能
        - **禁止营销工具**：除基础优惠券外，不提供复杂的营销活动管理工具
        - **禁止商家端独立管理界面**：商家通过用户端的"我的"页面进行基础管理，不设计独立的商家管理系统
      - **两级结构**：
        - **层级一：自定义分类 (可选)**：商家可自行创建商品/服务分类（如实体店的“上衣”、“裤子”，师傅的“管道疏通案例”、“家电维修案例”），并为分类添加概括性描述（可选）。
        - **层级二：独立商品/服务项/案例项 (核心)**：在“分类”下或直接在店铺下添加具体的“商品”、“服务项”或“案例项”。每一项包含：
          - **名称** (必填)
          - **产品/案例图集** (必填，至少上传1张图片，支持多图滑动查看，默认显示第1张)
          - **详细描述** (可选填，自定义文本，例如一包烟这类简单商品可不填)
          - **价格** (必填，默认为"面议"，商家可手动修改为具体数字价格。列表和卡片页不显示价格信息，仅在详情页中显示具体价格或"面议")
          - **单位/规格** (可选填，如"件"、"次"、"小时"、"斤"等)
      - **优惠券/体验券**：
        - 所有商铺类型均可选择性发布优惠券或体验券。
        - 用户可领取优惠券/体验券。
      - **评论功能**：
        - **开启条件**：仅当商家提供了优惠券或体验券，并且用户通过该券完成消费（即商家在后台进行了“核销”操作）后，用户才能对该次服务/商品进行评论。
        - **核销机制**：商家在其管理后台（如“我的”页面内）应有“核销”功能，可输入券码或扫描用户出示的券的二维码/条形码进行核销。
        - **评论内容**：可包括评分、文字描述，是否支持图片待定。
    - **配送要求 (Delivery Requirements)**：
      - **实体/网店/摊贩**：可设置“满XX元免费配送”或固定配送费等条件（需进一步明确具体配置方式）。
      - **师傅**：通常不涉及此类配送要求，主要为上门服务或约定地点服务。
    - **列表排序与筛选**：
      - **默认排序**：按距离由近及远。
      - **可选排序**：按最新发布、按优惠券（有券优先）。
      - **筛选**：支持基于商家自定义产品分类的筛选功能，用户可按一级分类或二级分类查看商品；同时商铺板块内搜索支持商品/服务名称和分类名称。
    - **板块内搜索**：
      - 用户在首页的搜索栏中输入关键词（例如“男装”、“女装”、“扳手”、“空调维修”等）。
      - 搜索结果将展示所有板块中，其“商品/服务项名称”、“店铺名称/昵称”或“主营业务/经营范围”等文本信息中包含这些关键词的条目。
      - 点击搜索结果，可以直接跳转到对应的商品/服务详情页或店铺主页。
    
    - **5.2.1.6 商铺认领功能 (Shop Claim Feature)**
      - **功能概述**：允许商家认领平台上已存在的商铺信息，获得管理权限
      - **认领条件**：
        - **登录要求**：用户必须先登录账号才能进行认领操作
        - **身份验证**：需要提供有效的联系电话进行身份验证
        - **审核机制**：平台将在1-3个工作日内联系申请人进行身份验证
      - **认领流程**：
        - **发起认领**：在商铺详情页点击"认领商铺"按钮
        - **确认信息**：系统显示商铺基本信息，用户确认认领意向
        - **联系方式验证**：输入联系电话，系统记录申请信息
        - **等待审核**：平台客服将主动联系进行身份验证
        - **认领成功**：审核通过后获得商铺管理权限
      - **认领后权限**：
        - **信息编辑**：可以编辑商铺基本信息、营业时间、服务内容等
        - **商品管理**：可以添加、编辑、删除商品/服务信息
        - **优惠券管理**：可以创建和管理优惠券/体验券
        - **呼叫服务**：可以设置和管理二维码呼叫服务
      - **防冲突机制**：
        - **唯一认领**：每个商铺只能被一个用户认领
        - **申诉处理**：如有认领争议，平台提供申诉渠道
        - **证据要求**：认领时需要提供营业执照、身份证等相关证明材料
    
    - **5.2.1.7 商铺举报功能 (Shop Report Feature)**
      - **功能概述**：为用户提供举报不当商铺信息的渠道，维护平台内容质量
      - **举报入口**：在商铺详情页提供"举报"按钮，位置显眼但不干扰正常浏览
      - **举报类型**：
        - **虚假信息**：商铺信息不真实、夸大宣传等
        - **服务质量问题**：服务态度差、服务质量不达标等
        - **价格欺诈**：价格虚高、隐性收费、不按约定收费等
        - **违法违规**：涉及违法经营、销售违禁品等
        - **其他问题**：用户可自定义描述其他问题
      - **举报流程**：
        - **选择举报原因**：从预设的举报类型中选择
        - **详细描述**：用户可选择性添加详细的问题描述
        - **提交举报**：确认提交举报信息
        - **处理反馈**：系统提示举报已提交，承诺24小时内处理
      - **处理机制**：
        - **快速响应**：承诺24小时内开始处理举报
        - **分类处理**：根据举报类型采用不同的处理方式
        - **结果通知**：处理完成后通过系统消息通知举报人
        - **记录保存**：保存举报记录，用于平台治理参考
      - **防恶意举报**：
        - **频次限制**：限制单个用户的举报频次
        - **内容审核**：对举报内容进行初步审核，过滤恶意举报
        - **反馈机制**：对于恶意举报行为进行记录和处理

  - **5.2.2 信息板块 (Information Section)**
    - **功能概述**：为本地用户提供一个免费发布和查找各类生活信息的平台，促进信息共享和互助。
    - **用户价值**：
      - **C端用户**：便捷地发布或查找本地的招聘、求职、房屋租售、二手买卖、寻人寻物等各类信息。
      - **信息发布者**：免费、快速地将信息传递给本地目标人群。
    - **界面布局设计**：
      - **顶部搜索区域**：
        - **城市选择按钮**：位于搜索栏左侧独立位置，支持手动切换城市/区域
        - **搜索输入框**：位于城市按钮右侧，支持关键词搜索，仅在信息板块内搜索
        - **布局方式**：水平排列 `[城市按钮] [搜索输入框]`，搜索框占据剩余空间
      - **子分类筛选区域**：
        - **排列方式**：水平滚动列表，支持左右滑动查看更多分类
        - **默认状态**："全部"分类为默认选中状态（高亮显示）
    - **核心子模块/类型 (固定，不可由用户自定义修改)**：
      - 全部
      - 招聘
      - 出租
      - 求租
      - 出售
      - 闲置
      - 求购
      - 求职
      - 其他
    - **通用基础信息字段 (适用于所有信息类型)**：
      - **信息标题** (必填)
      - **详细描述** (必填，支持图文混排，图片数量限制待定)
      - **联系电话** (必填，发布时需验证码，前端脱敏显示，点击可复制或拨打)
      - **价格** (可选填，默认为“面议”，用户可自定义输入具体数字价格)
      - **发布位置** (可选填，用户可选择是否显示信息相关的地理位置)
      - **信息发布/编辑验证**：用户发布或修改任何信息均需通过手机验证码进行验证。
      - **信息有效期**：
        - **设置要求**：必填。用户在发布信息时必须选择或设置信息的有效期限。
        - **目的**：确保平台信息的时效性，避免过期信息干扰用户，提升信息质量。
        - **预设选项**：系统应提供一组合理的预设有效期选项，例如：7天、15天、30天（默认选择15天）。
        - **自定义选项**（可选）：可考虑允许用户选择“长期有效”（例如，系统内部标记为90天或更长，并有相应管理机制）或自定义具体天数（例如，不超过30天或90天，需明确上限）。
        - **到期处理**：
          - **自动下架/标记**：信息到达有效期后，系统应自动将其下架（从正常列表移除，但发布者仍可在“我的发布”中看到并管理）。
          - **到期提醒**：在信息到期前（例如，提前1天），系统应通过站内信或推送通知提醒发布者信息即将到期。
          - **续期/重新发布**：发布者应可以在信息到期前或到期后，选择对信息进行“续期”（延长有效期，可能涉及付费）或“重新发布”（信息将以新的发布时间重新出现在列表前列，可能视为新信息发布，同样可设置有效期）。
        - **卡片展示**：对于有效期较短或即将到期的信息，可在信息卡片上酌情显示有效期提醒，如“还剩3天结束”。
    - **列表排序与筛选**：
      - **默认排序**：按最新发布时间。
      - **筛选**：按信息类型筛选。
    - **全局搜索联动**：
      - 用户在首页的搜索栏中输入关键词。
      - 搜索结果将展示所有板块中，其“信息标题”、“详细描述”等文本信息中包含这些关键词的条目。
      - 点击搜索结果，可以直接跳转到对应的信息详情页。

    - **信息卡片核心信息定义 (Information Card Core Information Definition)**：
      - **目标**：统一信息板块各类信息（招聘、出租、闲置、求职等）在列表中的卡片式展示风格，确保信息传递的清晰、高效和一致性，同时兼顾不同信息类型的特性。
      - **设计原则**：简洁明了、重点突出、易于扫读、引导点击。
      - **核心信息点与交互**：
        - **1. 信息类型标签 (Information Type Tag)**
          - **优先级**：高
          - **说明**：在卡片左上角或右上角用醒目颜色标签标明信息类型，如“招聘”、“出租”、“闲置”、“求购”等。这有助于用户快速识别信息类别。
          - **交互**：无特殊交互，仅为视觉标识。
        - **2. 信息标题 (Information Title)**
          - **优先级**：高
          - **说明**：清晰、完整地展示用户发布的信息标题，通常占据卡片最主要的位置，字体稍大或加粗。
          - **交互**：点击卡片任何区域（除特定按钮外）均可进入信息详情页。
        - **3. 价格/薪资范围 (Price/Salary Range)**
          - **优先级**：中 (根据信息类型调整，如招聘、出售、出租等类型优先级高)
          - **说明**：显示用户设定的价格或薪资范围。若未填写，则显示“面议”。对于招聘信息，可显示如“5K-8K/月”；对于物品，可显示如“¥200”。
          - **交互**：无特殊交互。
        - **4. 关键信息摘要/图片缩略图 (Key Info Snippet/Image Thumbnail)**
          - **优先级**：中
          - **说明**：
            - **文本摘要**：对于无图或图片不重要的信息（如纯文字求职、求助），可展示“详细描述”的前20-30字摘要。
            - **图片展示**：对于有图且图片重要的信息（如闲置物品出售、房屋出租等）：
              - **默认显示**：卡片上最多直接展示4张缩略图。如果图片总数少于或等于4张，则全部展示。
              - **多图提示**：如果图片总数超过4张，则在第4张缩略图上叠加一个明确的角标提示，例如“共X张”或“更多”图标，以告知用户还有更多图片。
              - **自定义内容**：图片下方的描述或详情等内容，可以在发布时由用户自定义填写，并在卡片上酌情显示部分（例如，关键描述的前几句）。
          - **交互**：
            - **查看大图/更多图片**：点击任一张缩略图，应直接在当前页面以弹窗或覆盖层的方式打开图片浏览器（Viewer模式），支持左右滑动查看所有上传的高清大图，并可双指缩放。此图片浏览器应包含关闭按钮，关闭后返回卡片列表。此交互方式取代了原先的“点击图片进入详情页查看大图”。
            - **文本摘要交互**：若展示的是文本摘要，点击摘要区域，应直接在当前卡片内展开显示完整的“详细描述”内容，无需跳转到新的详情页面。卡片高度会动态调整以适应展开后的全部文本。再次点击或提供一个明确的“收起”按钮可以收回展开的内容，恢复卡片原始高度。
        - **5. 发布位置与距离 (Location & Distance - 可选)**
          - **优先级**：低 (根据信息类型和用户是否填写决定是否展示)
          - **说明**：
            - **位置展示**：如果用户填写了发布位置，且该信息类型与地理位置相关（如房屋出租、线下招聘、二手交易的线下自提点等），则在卡片上用小字号或图标+地名的方式展示，如“[位置图标] XX小区附近”。
            - **距离显示**：在获取到用户当前地理位置授权的前提下，如果信息包含有效的地理坐标，应计算并显示信息发布位置与用户当前位置的相对距离，例如“距您约500米”、“1.2公里内”。此距离信息应与位置地名一起展示，或在附近。
          - **交互**：点击位置信息或距离信息，应直接调用地图应用并导航至该位置（如果可能，优先使用小程序内置地图，否则调用外部地图应用）。如果因未授权等原因无法获取用户当前位置，则不显示距离，仅显示发布者设定的位置名称。
        - **6. 联系电话按钮/方式 (Contact Button/Method)**
          - **优先级**：高
          - **说明**：提供清晰的“电话”或“联系”按钮/图标。电话号码在卡片上不直接显示。
          - **交互**：点击按钮后，直接调用系统的拨号功能，预填号码并发起呼叫。如果用户提供了其他联系方式（如微信），详情页可展示。
        - **7. 发布时间/新鲜度 (Post Time/Freshness)**
          - **优先级**：低
          - **说明**：在卡片不显眼位置（如右下角）显示信息的发布时间，例如“刚刚发布”、“X分钟前”、“X小时前”，帮助用户判断信息时效性。
          - **交互**：无特殊交互。
        - **8. 转发按钮 (Forward Button)**
          - **优先级**：中
          - **说明**：在卡片上提供一个“转发”或“分享”按钮，允许用户将该信息通过微信等社交渠道转发给朋友或群组。
          - **交互**：点击后，生成包含该信息核心内容（如标题、价格、联系方式等）的文本或小程序卡片消息，调用微信的分享接口。
        - **9. 卡片整体点击交互 (Overall Card Click Interaction)**
          - **优先级**: N/A (通用行为)
          - **说明**: 为贯彻“无详情页”设计原则，确保用户在卡片列表即可获取完整信息。
          - **交互**:
            - 点击卡片上除已定义的特定交互元素（如图片缩略图、联系按钮、转发按钮、已展开文本的“收起”按钮等）外的任何区域（例如：标题、价格、地理位置、发布时间、卡片空白区域）：
              - 若“详细描述”内容当前为摘要状态或未完全显示，则在卡片内直接展开完整的“详细描述”内容。卡片高度将动态调整以适应展开后的文本。
              - 若“详细描述”内容已完全展开，则此类点击无额外操作。
            - 此交互行为取代了任何可能导致跳转到独立详情页面的操作。

  - **5.2.3 出行板块 (Transportation Section)**
    - **功能概述**：为本地用户提供拼车、货运、代驾等出行相关服务的信息发布与匹配平台。
    - **用户价值**：
      - **C端用户**：便捷地找到合适的出行服务，降低出行成本，提高出行效率。
      - **服务提供者**：扩大客源，提高车辆/服务利用率，增加收入。
    - **界面布局设计**：
      - **顶部搜索区域**：
        - **城市选择按钮**：位于搜索栏左侧独立位置，支持手动切换城市/区域
        - **搜索输入框**：位于城市按钮右侧，支持关键词搜索，仅在出行板块内搜索
        - **布局方式**：水平排列 `[城市按钮] [搜索输入框]`，搜索框占据剩余空间
      - **子分类筛选区域**：
        - **排列方式**：水平滚动列表，支持左右滑动查看更多分类
        - **默认状态**："全部"分类为默认选中状态（高亮显示）
    - **核心子模块/类型 (固定，不可由用户自定义修改)**：
      - 全部
      - 车找人（司机发布，寻找乘客）
      - 人找车（乘客发布，寻找车辆）
      - 货找车（货主发布，寻找运输车辆）
      - 车找货（司机发布，寻找货源）
      - 代驾服务（代驾司机发布服务信息）
    - **通用基础信息字段**：
      - **出行标题** (必填)
      - **出发地点** (必填)
      - **目的地** (必填)
      - **出发时间** (必填)
      - **联系电话** (必填，发布时需验证码)
      - **价格/费用** (可选填，默认为"面议")
      - **备注说明** (可选填)
      - **信息发布/编辑验证**：发布或修改信息需通过手机验证码验证

    - **出行卡片核心信息定义 (参考商铺卡片，确保风格统一)**：
      - **1. 出行类型标签 (Type Tag)**
        - **优先级**：高
        - **说明**：根据发布者选择的子模块类型（如“车找人”、“人找车”、“货找车”、“车找货”、“代驾服务”）在卡片显著位置（如左上角或标题前）展示清晰的类型标签，建议使用不同颜色或图标区分。
        - **交互**：无特殊交互，仅为信息标识。
      - **12. 体验券 (Experience Voucher)**
        - **优先级**：中 (服务提供者可选配置)
        - **说明**：允许服务提供者（如代驾司机、特定线路的拼车）配置和发放体验券，用于吸引新用户或推广特定服务。卡片上应有明确的“体验券”标识或入口，例如“领券”按钮或标签。
        - **目的与功能**：与其他模块的体验券功能一致，旨在通过提供初次体验优惠，促进用户转化和提高服务使用率。用户领取后可在满足条件时使用以抵扣部分费用或享受特定体验服务。
        - **交互**：点击“领券”按钮或标识，弹出体验券领取界面或直接领取成功提示。用户可在个人中心查看已领取的体验券。在下单或支付环节，可选择使用符合条件的体验券。
      - **2. 出行标题 (Title)**
        - **优先级**：高
        - **说明**：清晰展示用户发布的出行标题，应简洁明了，突出核心信息。
        - **交互**：点击卡片任何区域（除特定按钮外）均可进入信息详情页。
      - **3. 出发地与目的地 (Origin & Destination)**
        - **优先级**：高
        - **说明**：清晰展示出发地和目的地信息，建议使用“起点图标”和“终点图标”进行视觉引导，格式如：[起点图标] XXX小区 - [终点图标] XXX机场。
        - **交互**：无特殊交互。
      - **4. 出发时间 (Departure Time)**
        - **优先级**：高
        - **说明**：明确展示用户设定的出发时间，格式需统一，如“MM月DD日 HH:mm”或“明天 HH:mm”。
        - **交互**：无特殊交互。
      - **5. 价格/费用 (Price/Fee)**
        - **优先级**：中
        - **说明**：显示用户设定的价格或费用。如果用户未填写，则显示“面议”。如果填写具体数字，则显示如“¥50”或“50元”。
        - **交互**：无特殊交互。
      - **6. 备注说明摘要 (Remarks Snippet)**
        - **优先级**：中
        - **说明**：如果用户填写了备注说明，卡片上可展示部分摘要内容（如前20-30字），超出部分用“...”省略。完整内容在详情页展示。
        - **交互**：无特殊交互。
      - **7. 电话按钮/联系方式 (Phone Button/Contact)**
        - **优先级**：高
        - **说明**：卡片上应有明显的“电话”或“联系”按钮。电话号码在卡片上可以不直接显示。
        - **交互**：点击按钮后，直接调用系统的拨号功能，预填号码并发起呼叫。
      - **新增：转发按钮 (Forward Button)**
        - **优先级**：中
        - **说明**：在卡片上提供一个“转发”或“分享”按钮，允许用户将该出行信息通过微信等社交渠道转发给朋友或群组。
        - **交互**：点击后，生成包含该出行信息核心内容（如标题、起止点、时间、联系方式等）的文本或小程序卡片消息，调用微信的分享接口。
      - **8. 发布时间/新鲜度 (Post Time/Freshness)**
        - **优先级**：低
        - **说明**：在卡片不显眼位置（如右下角）显示信息的发布时间，例如“刚刚发布”、“X分钟前”、“X小时前”、“X天前”，帮助用户判断信息时效性。
        - **交互**：无特殊交互。
      - **9. 距离显示 (Distance - 可选，基于场景)**
        - **优先级**：中 (视具体子类型和用户场景而定)
        - **说明**：对于“车找人”、“人找车”等场景，如果用户的当前位置与信息的“出发地”或“目的地”在同一城市或合理范围内，可以考虑显示用户与该地点的直线距离，帮助用户判断便捷性。其他如“货找车”、“车找货”可能不太需要。
        - **交互**：无特殊交互。
      - **10. 线路类型标签 (Route Type Tag - 固定/非固定)**
        - **优先级**：高 (发布时必选)
        - **说明**：对于“车找人”、“人找车”、“车找货”、“货找车”等涉及线路的出行信息，发布者在发布时必须选择“固定线路”或“非固定线路”。此标签将在卡片上展示，帮助用户筛选。
        - **交互**：无特殊交互，仅为信息标识。
      - **11. 服务范围标签 (Service Area Tag - 城区/跨城)**
        - **优先级**：高 (发布时必选)
        - **说明**：发布者在发布时必须选择服务范围：“城区”或“跨城”。此标签将在卡片上展示，帮助用户快速判断服务是否覆盖其需求范围。
        - **交互**：无特殊交互，仅为信息标识。

  - **5.2.4 跑腿板块 (Errand Services Section)**
    - **功能概述**：为本地用户提供代买、代送、代办等跑腿服务的信息发布与展示平台。
    - **认证要求**：
      - **发布跑腿信息**：必须通过实名认证的用户才能发布
      - **认证流程**：需要后台人工审核，审核通过后获得认证标识
    - **用户价值**：
      - **需求方**：便捷地找到可靠的跑腿服务，解决生活中的各种代办需求
      - **服务提供者**：通过提供跑腿服务获得收入，灵活安排工作时间
    - **核心子模块/类型**：
      - 代买（代购商品）
      - 代送（代送物品）
      - 代办（代办事务）
      - 其他跑腿（其他类型的跑腿服务）
    - **通用基础信息字段**：
      - **跑腿标题** (必填)
      - **服务类型** (必填，选择上述子模块)
      - **详细描述** (必填)
      - **服务地点** (必填)
      - **期望完成时间** (必填)
      - **服务费用** (必填)
      - **联系电话** (必填，发布时需验证码)





            - 首次尝试查看“私密信息备注”时，系统必须弹窗强提醒：“【警告】您即将查看用户提供的敏感信息（如取件码、密码等），此信息高度机密。请确认您能负责任地处理此信息并完成服务。一旦查看，若非不可抗力取消订单，或泄露信息，将可能导致您的账号受到严厉处罚（包括但不限于警告、永久封禁等），并需承担由此给用户造成的一切损失。平台不为此类行为承担任何责任。是否确认查看？” 提供“我已了解并同意承担责任，确认查看”和“取消”按钮。
            - 在卡片列表和公开的详情页中，此信息不展示。
        - **后端处理与通知机制**：
          - **存储与权限**：需加密存储，并严格控制访问权限，确保只有成功接单的服务提供者才能在订单进行中状态下解密查看，并记录查看行为。
          - **订单发布者通知**：当服务方首次成功查看敏感信息后，系统应立即向订单发布者发送系统通知：“【重要提醒】您订单（订单号：XXXXX）的私密信息已被服务方【服务方昵称】查看。请密切关注订单进展并与服务方保持沟通。若在服务过程中出现任何纠纷或疑似欺诈行为，建议您优先与服务方协商解决，必要时可选择报警处理。平台作为信息撮合方，不参与用户间的具体交易和纠纷处理。”
          - **服务方取消订单及纠纷处理原则**：
            - 平台作为信息发布和撮合平台，不直接参与用户与服务方之间的具体服务交易及后续可能产生的任何纠纷。所有因服务产生的争议、损失或责任，均由用户与服务方双方自行协商解决，或通过法律途径解决，平台对此不承担任何直接或间接责任。
            - 若服务方在查看敏感信息后，存在恶意行为（如无故取消订单、泄露信息等），用户可向平台举报。平台将依据社区规则和用户协议，对被核实存在违规行为的服务方账号采取相应管理措施（如警告、限制功能、封禁账号等），但这不代表平台介入双方的民事纠纷或承担任何赔偿责任。
            - 平台强烈建议双方在交易前充分沟通，明确服务内容、费用、责任等，并保留好相关沟通记录和证据，以备不时之需。
      - **认证状态显示** (系统自动显示用户认证状态)

  - **5.2.5 发布功能模块 (Publishing Function)**
    - **功能概述**：统一的信息发布入口，通过弹窗方式引导用户选择发布板块。
    - **发布流程设计**：
      - **步骤1**：用户点击"发布"按钮
      - **步骤2**：弹窗显示四大板块选择（商铺、信息、出行、跑腿）
      - **步骤3**：用户选择板块后跳转到相应的发布界面
      - **步骤4**：用户选择具体类型并填写相关内容
      - **步骤5**：填写手机验证码完成发布（验证码代替审核机制）
    - **发布权限控制**：
      - **一般信息发布**：所有用户均可发布，需手机验证码验证
      - **跑腿板块发布**：仅认证用户可发布跑腿信息
      - **发布审核**：除跑腿板块需认证外，其他信息发布不需要审核，通过验证码即可发布
    - **弹窗界面设计**：
      - 四大板块图标化展示
      - 每个板块配有简短说明文字
      - 支持快速选择和跳转
      - 货找车（货主发布，寻找货车）
      - 车找货（货车司机发布，寻找货源）
      - 代驾服务
    - **通用基础信息字段 (适用于所有出行类型)**：
      - **服务标题** (必填，如"市区到机场拼车"、"搬家货运服务")
      - **出发地点** (必填，支持地图选点或文字描述)
      - **目的地点** (必填，支持地图选点或文字描述)
      - **出发时间** (必填，支持具体时间或时间段)
      - **联系电话** (必填，发布时需验证码，前端脱敏显示)
      - **价格** (可选填，默认为"面议"，可输入具体价格)
      - **车辆信息** (可选填，车型、车牌号、座位数等)
      - **详细说明** (可选填，补充说明、特殊要求等)
      - **信息发布/编辑验证**：用户发布或修改任何信息均需通过手机验证码进行验证。
    - **列表排序与筛选**：
      - **默认排序**：按最新发布时间。
      - **筛选功能**：
        - **基础分类筛选**：按出行类型（全部、车找人、人找车、货找车、车找货、代驾服务）
        - **线路类型筛选**：固定线路/非固定线路（默认选择：非固定线路）
          - **固定线路**：企业/公司运营的定期班车、固定路线服务
          - **非固定线路**：个人发布的灵活出行需求
        - **服务范围筛选**：城区/跨城区（默认选择：城区）
          - **城区**：本地城区内的短途出行服务
          - **跨城区**：跨城市、长途出行服务（包含乡镇）
        - **筛选交互**：线路类型和服务范围筛选独立于默认排序功能，用户可单独或组合使用
    - **全局搜索联动**：
      - 支持按出发地、目的地、服务类型等关键词搜索。

  - **5.2.4 跑腿板块 (Errand Services Section)**
    - **功能概述**：为本地用户提供代买、代送、代办等跑腿服务的需求发布与信息展示平台。
    - **用户价值**：
      - **需求方**：解决临时性、紧急性的生活服务需求，节省时间成本。
      - **服务提供者**：利用空闲时间提供服务，获得额外收入。
    - **界面布局设计**：
      - **顶部搜索区域**：
        - **城市选择按钮**：位于搜索栏左侧独立位置，支持手动切换城市/区域
        - **搜索输入框**：位于城市按钮右侧，支持关键词搜索，仅在跑腿板块内搜索
        - **布局方式**：水平排列 `[城市按钮] [搜索输入框]`，搜索框占据剩余空间
      - **子分类筛选区域**：
        - **排列方式**：水平滚动列表，支持左右滑动查看更多分类
        - **默认状态**："全部"分类为默认选中状态（高亮显示）
    - **核心子模块/类型 (固定，不可由用户自定义修改)**：
      - 全部
      - 代买（代购商品）
      - 代送（配送物品）
      - 代办（代办事务）
      - 其他跑腿
    - **通用基础信息字段 (适用于所有跑腿类型)**：
      - **服务标题** (必填，如"代买药品"、"文件代送")
      - **服务详情** (必填，详细描述需要完成的任务)
      - **服务地点** (必填，任务执行的具体地址)
      - **完成时间要求** (必填，期望完成的时间)
      - **联系电话** (必填，发布时需验证码，前端脱敏显示)
      - **服务费用** (必填，默认为"面议"，可输入具体金额)
        - **信息发布/编辑验证**：用户发布或修改任何信息均需通过手机验证码进行验证。
    - **联系机制**：
      - 用户可浏览跑腿信息并通过联系方式直接联系发布者。
      - 平台仅提供信息展示，用户可直接在卡片上编辑或删除自己发布的信息，无需额外的详情页面操作。
    - **列表排序与筛选**：
      - **默认排序**：按最新发布时间。
      - **筛选**：按服务类型、完成时间、费用区间筛选。
    - **全局搜索联动**：
      - 支持按服务类型、地点等关键词搜索。


      - 不存在置顶功能，所有内容按时间或距离等规则自然排序

  - **5.2.6 发布功能 (Publishing)**
    - **功能概述**：统一的信息发布入口，支持各板块信息的创建和编辑。
    - **发布流程**：
      - **选择板块和类型**：用户首先选择要发布的板块（商铺/出行/信息/跑腿）和具体类型
      - **填写基础信息**：根据选择的类型，动态显示对应的表单字段
      - **上传图片**：支持多图上传，自动压缩和格式转换
      - **位置选择**：支持地图选点或手动输入地址
      - **手机验证码验证**：发布前必须通过手机验证码验证（验证码代替审核机制）
      - **即时发布**：验证码通过后信息立即发布上线，无需等待人工审核
    - **发布权限控制**：
     - **一般信息发布**：所有用户均可发布，仅需手机验证码验证
      - **跑腿板块特殊要求**：必须通过实名认证的用户才能发布
    - **编辑和管理**：
      - 用户可在"我的发布"中查看、编辑、删除自己发布的信息
      - **界面内快速编辑删除**：在信息板块、出行板块、跑腿板块的信息卡片中，发布者可直接看到编辑和删除按钮
        - **权限控制**：编辑删除按钮仅对内容发布者本人可见，其他用户无法看到
        - **操作保护**：删除前弹出确认框（"你确定要删除此条信息吗？"），避免误删
        - **操作反馈**：删除后显示Toast提示（"已删除"或"内容已更新"），提供明确的操作反馈
        - **软删除机制**：删除操作采用软删除方式，先隐藏内容再进行数据处理
      - 编辑信息同样需要手机验证码验证
    - **后台管理授权功能**：
      - 平台可为特定用户授予后台管理权限
      - 可选择性授权不同的管理内容（如用户管理、内容审核、数据查看等）
      - 被授权用户可以免验证码发布信息
      - 授权用户具有内容管理和用户管理的部分权限

  - **5.2.7 消息中心 (Message Center)**
    - **功能概述**：统一管理用户的各类消息通知和私信交流。
    - **消息类型**：
      - **系统通知**：审核结果、违规提醒、功能更新等。
      - **互动消息**：评论、点赞、收藏等用户互动通知。
      - **私信交流**：用户之间的一对一沟通。
      - **服务提醒**：出行匹配等服务相关提醒。
    - **消息管理**：
      - 支持消息标记已读/未读。
      - 支持消息删除和批量操作。
      - 重要消息置顶显示。

  - **5.2.8 个人中心 (User Center)**
    - **功能概述**：用户个人信息管理和应用设置中心。
    - **核心功能模块**：
      - **个人信息**：头像、昵称、手机号、实名认证状态等。
      - **我的发布**：查看、编辑、删除已发布的信息，按板块分类展示。
      
      - **认证管理**：实名认证、商家认证等资质认证入口。
      - **扫码呼叫**：扫描平台生成的专用二维码发起呼叫服务，仅识别平台内部二维码。
      - **我的优惠券/体验券**：
        - **付费开通机制**：用户需自行扫码付款开通优惠券/体验券功能
        - **付费标准**：40元/月、200元/半年、300元/年
        - **功能权限**：开通后可发布优惠券/体验券，吸引用户到店消费
        - **券类管理**：创建、编辑、删除优惠券/体验券
        - **核销功能**：扫码或输入券码进行核销，记录核销历史
        - **数据统计**：查看券的领取量、核销量、转化率等数据
        - **到期提醒**：付费服务到期前提醒续费
      - **我的呼叫服务**：
        - **功能定位**：为商家提供二维码呼叫服务管理功能，用户扫码后可直接呼叫商家服务
        - **适用场景**：餐厅点餐、酒店服务、商场导购、医院挂号等需要即时服务响应的场景
        - **语言支持**：呼叫服务功能仅支持中文，不提供其他语言版本
        - **二维码生成与管理**：
          - **新增二维码**：创建新的服务场景二维码，配置服务内容和参数
          - **编辑二维码**：修改已有二维码的服务配置、欢迎语、提示音等
          - **二维码预览**：支持大图预览，确认二维码显示效果
          - **下载打印**：支持高清二维码下载，便于打印张贴
          - **批量管理**：支持多个二维码的统一管理和状态控制
        - **服务场景配置**：
           - **场景名称**：自定义服务场景名称（如"餐桌1号"、"前台服务"等）
           - **服务内容**：完全自定义服务项目，商家可根据实际需求添加任意服务选项
           - **服务时间**：设置服务可用时间段，超出时间显示暂停服务提示
           - **欢迎语设置**：自定义用户扫码后看到的欢迎文案
           - **成功提示语**：自定义呼叫成功后的提示文案
           - **提示铃声**：选择呼叫提示音效，支持多种铃声选择
           - **备注开关**：设置是否允许用户添加备注信息
           - **自动关闭设置**：设置呼叫推送消息的自动关闭时间（默认2分钟，可自定义）
           - **评价接收人设置**：
             - **默认接收**：服务人员自己接收用户评价
             - **指定接收人**：可设置经理或其他人员手机号接收评价
             - **验证码验证**：设置接收人时需要手机验证码确认
             - **评价隔离**：指定接收人后，服务人员无法查看评价内容
        - **二维码安全机制**：
           - **平台专用识别**：二维码仅能被平台扫码功能识别，其他扫码软件无效
           - **防拍照技术**：采用动态加密技术，打印后拍照的二维码无法被识别
           - **防恶意替换**：二维码包含商家身份验证信息，防止被恶意替换
           - **长期有效性**：二维码长期有效，除非物理损坏、服务人员主动替换或后台删除
           - **打印安全提示**：二维码打印页面包含安全使用提示，与二维码一同打印张贴
           - **安全提示内容**："安全起见请打开微信搜索'本地助手'小程序或打开微信小程序'本地助手'选择二维码扫码"
        - **呼叫记录管理**：
           - **实时推送消息**：用户呼叫后立即推送消息到商家端
           - **消息交互机制**：
             - **确认按钮**：服务人员可点击确认按钮表示已接收处理
             - **等待模式**：服务人员可选择等待，消息保持显示状态
             - **自动关闭**：默认3分钟后自动关闭推送消息（时间可自定义设置）
           - **消息详情查看**：查看呼叫时间、服务内容、用户备注等详细信息
           - **处理状态标记**：标记呼叫处理状态（待处理、处理中、已完成）
           - **历史记录查询**：按时间、服务类型等条件查询历史呼叫记录
        - **用户评价管理**：
           - **直接评价功能**：用户扫码后可直接进行评价，无需先呼叫服务
           - **评价接收机制**：
             - **默认接收**：评价默认发送给二维码创建的服务人员
             - **指定接收人**：服务人员可设置经理或其他人员接收评价
             - **接收人验证**：设置接收人时需要提供手机号并通过验证码确认
             - **评价隔离**：指定接收人后，原服务人员无法查看评价内容
           - **评价记录查看**：查看用户对服务的星级评价和文字反馈
           - **评价回复功能**：评价接收人可对用户评价进行回复和感谢
           - **评价数据统计**：统计平均评分、评价趋势等数据
           - **服务改进参考**：基于评价数据优化服务质量
      - **收藏夹**：收藏感兴趣的商铺、信息等。
      - **浏览历史**：查看最近浏览的内容记录。
      - **设置**：隐私设置、通知设置、账号安全等。
      - **帮助与反馈**：使用帮助、问题反馈、客服联系等。
      - **服务条款与隐私政策**：相关法律文档查看。

  - **5.2.9 管理后台 (Admin Dashboard)**
    - **功能概述**：为平台管理员提供内容审核、用户管理、数据统计等管理功能，支持授权管理。
    - **核心功能模块**：
      - **用户管理**：用户信息查看、状态管理、违规处理等
      - **四大板块内容管理**：
        - **商铺板块管理**：
          - **商铺列表管理**：查看所有商铺信息，包括商铺名称、经营者、联系方式、认证状态、创建时间等
          - **商铺详情查看**：查看商铺的完整信息、发布的产品/服务、优惠券、用户评价等
          - **商铺状态管理**：支持启用、禁用、封禁商铺操作
          - **商铺分类管理**：管理商铺分类（实体店铺、在线网店、流动摊贩、上门师傅）
          - **商铺数据统计**：统计各类商铺数量、活跃度、收入贡献等
          - **商铺违规处理**：对违规商铺进行警告、限制、封禁等处理
        - **信息板块管理**：
          - **信息列表管理**：查看所有发布的信息，包括标题、发布者、分类、状态、发布时间等
          - **信息详情查看**：查看信息的完整内容、图片、联系方式等
          - **信息状态管理**：支持上线、下线、删除信息操作
          - **信息分类管理**：管理信息分类（招聘、出租、求租、出售、闲置、求购、求职、其他）
          - **信息质量监控**：检测重复发布、虚假信息、违规内容等
        - **出行板块管理**：
          - **出行信息管理**：查看所有出行信息，包括路线、发布者、车辆信息、联系方式等
          - **出行分类管理**：管理出行分类（车找人、人找车、货找车、车找货、代驾服务）
          - **出行安全监控**：监控可疑的出行信息，防范安全风险
        - **跑腿板块管理**：
          - **跑腿信息管理**：查看所有跑腿需求和服务信息
          - **跑腿分类管理**：管理跑腿分类（代买代购、代送服务、代办事务、其他跑腿）
          - **跑腿服务者认证管理**：管理跑腿服务者的实名认证和资质审核
      - **内容审核**：待审核信息列表、审核操作、审核记录等（主要针对跑腿板块认证）
      - **举报处理**：用户举报内容的处理和跟进
      - **主动内容监控**：
        - **违规内容检测**：
          - **关键词过滤**：设置违规关键词库，自动检测可能违规的内容
          - **图片内容审核**：对上传的图片进行违规内容检测
          - **重复内容检测**：识别重复发布、刷屏等行为
          - **虚假信息识别**：通过算法和人工审核识别虚假信息
        - **异常行为监控**：
          - **频繁发布监控**：监控用户异常频繁的发布行为
          - **恶意注册检测**：识别批量注册、虚假注册等行为
          - **刷评价监控**：检测虚假评价、刷好评等行为
        - **风险预警机制**：
          - **内容风险评级**：对发布的内容进行风险等级评估
          - **用户风险标记**：标记高风险用户和行为模式
          - **自动处理规则**：设置自动下线、限制发布等处理规则
      - **数据统计**：用户数据、内容数据、活跃度统计等
      - **系统设置**：平台参数配置、功能开关、公告管理等
      - **认证管理**：实名认证、商家认证的审核和管理
      - **违规管理**：违规内容处理、用户封禁、黑名单管理等
      - **授权管理功能**：
        - **用户授权**：可为特定用户授予后台管理权限
        - **权限配置**：可选择性授权不同的管理内容（用户管理、内容审核、数据查看等）
        - **授权记录**：记录所有授权操作和权限变更历史
        - **权限监控**：监控被授权用户的操作行为
      - **商家认领管理功能**：
        - **认领申请管理**：
          - **申请列表**：显示所有商家认领申请，包含申请时间、商家信息、申请状态
          - **申请详情**：查看认领申请的详细信息，包括商家填写的店铺信息、联系方式等
          - **审核操作**：支持通过、拒绝、要求补充材料等审核操作
          - **审核记录**：记录每次审核操作的时间、操作人、审核结果和备注
        - **认领状态跟踪**：
          - **待处理申请**：新提交的认领申请，等待管理员审核
          - **审核中申请**：正在进行人工核实或实地验证的申请
          - **已通过申请**：审核通过并完成认领的申请记录
          - **已拒绝申请**：审核未通过的申请及拒绝原因
        - **用户行为记录管理**：
          - **稍后处理记录**：记录用户选择"稍后处理"的商家认领提示
            - **记录信息**：用户手机号、匹配的商家信息、选择时间、后续跟进状态
            - **跟进提醒**：系统自动提醒管理员对"稍后处理"的用户进行电话跟进
            - **跟进记录**：记录管理员的跟进时间、联系结果、用户反馈
          - **拒绝认领记录**：记录用户明确表示"不是我的店铺"的情况
            - **数据纠错**：标记可能存在错误的商家数据，安排现场核实
            - **误匹配分析**：分析手机号与商家信息的匹配准确性，优化匹配算法
            - **数据清理**：对确认错误的匹配关系进行数据清理和修正
        - **现场核实管理**：
          - **核实任务列表**：需要现场核实的商家信息列表
          - **核实计划**：安排现场核实的时间、地点、负责人
          - **核实结果记录**：记录现场核实的结果、照片、商家反馈等
          - **数据更新**：基于核实结果更新商家信息或删除错误数据
        - **认领数据统计**：
          - **认领成功率**：统计商家认领的成功率和转化情况
          - **用户行为分析**：分析用户对认领提示的响应情况
          - **数据质量评估**：评估采集商家数据的准确性和完整性
          - **核实效率统计**：统计现场核实的效率和成本
      - **广告管理功能**：
        - **广告位管理**：设置广告在内容列表中的穿插频率和位置
        - **广告内容管理**：上传、编辑、删除广告内容
        - **链接管理**：设置广告的外部链接或纯展示模式
        - **广告效果统计**：点击率、展示量等数据分析
      - **呼叫服务数据统计**（仅平台PC管理端可见）：
        - **引流数据分析**：统计呼叫服务为平台带来的用户流量和活跃度
        - **功能使用统计**：分析呼叫服务的使用频次、热门服务类型、用户行为模式
        - **商家参与度**：统计使用呼叫服务的商家数量、活跃商家排名
        - **用户满意度**：汇总评价数据，分析用户对呼叫服务的满意度趋势
        - **市场潜力分析**：基于使用数据评估呼叫服务功能的市场需求和发展潜力
        - **地域分布统计**：分析不同地区呼叫服务的使用情况和普及程度
    - **权限分级**：
      - **超级管理员**：拥有所有权限，包括授权其他用户
      - **授权管理员**：根据授权范围拥有部分管理权限
      - **普通用户**：无后台管理权限

## 6. 用户体验与交互设计指导

- **6.0 设计规范总则与平台定位**
  - **6.0.1 产品定位明确**：
    - **信息撮合平台**：专注于本地信息发布与查询，非电商平台
    - **轻量化工具**：避免复杂的管理功能和数据分析界面
    - **微信小程序生态**：严格遵循微信小程序设计规范和技术限制
  - **6.0.2 设计禁止事项**：
    - **禁止电商化设计**：不得设计订单管理、购物车、支付流程等电商界面
    - **禁止复杂管理界面**：不得设计独立的商家后台管理系统
    - **禁止Web技术元素**：不得使用HTML标签、CSS样式、Web图标库
    - **禁止数据分析界面**：不得设计复杂的图表、报表、统计分析界面
  - **6.0.3 技术栈设计要求**：
    - **必须使用微信小程序组件**：button、view、text、image等原生组件
    - **禁止Web组件**：不得使用div、span、p、h1-h6等HTML标签
    - **图标使用规范**：使用微信小程序官方图标或符合规范的图标
    - **样式实现方式**：使用WXSS而非CSS进行样式定义

- **6.1 必需界面设计要求**
  - **6.1.1 用户端必需界面清单**
    - **登录界面**：微信授权登录、手机号登录等
    - **主要功能界面**：商铺、信息、出行、跑腿四大板块界面
    - **产品/服务分类列表界面**：
      - **商家产品列表界面**：展示商家发布的所有产品/服务，支持分类筛选
      - **信息分类列表界面**：按分类展示信息内容（招聘、出租、求租、出售、闲置、求购、求职、其他）
      - **出行分类列表界面**：按分类展示出行信息（车找人、人找车、货找车、车找货、代驾服务）
      - **跑腿分类列表界面**：按分类展示跑腿信息（代买代购、代送服务、代办事务、其他跑腿）
    - **详情界面系列**：
      - **商家详情界面**：展示商家完整信息、联系方式、位置、评价等
      - **产品/服务详情界面**：展示具体产品/服务的详细信息、价格、图片、联系方式
      - **信息详情界面**：展示信息发布的完整内容、联系方式、位置等
      - **出行详情界面**：展示出行信息的详细内容、路线、联系方式等
      - **跑腿详情界面**：展示跑腿服务的详细要求、联系方式等
    - **发布界面**：统一发布入口，通过弹窗选择板块（商铺、信息、出行、跑腿）后跳转到相应发布表单
    - **个人中心界面**：包含以下必需子界面
      - **实名认证界面**：身份证上传、人脸识别等认证流程
      - **我的优惠券界面**：券的管理、核销等功能
      - **我的呼叫服务界面**：二维码管理、呼叫记录、评价管理等功能
        - **二维码管理子界面**：新增、编辑、预览、下载二维码
        - **服务配置子界面**：配置服务场景、内容、时间、欢迎语等
        - **呼叫记录子界面**：查看实时推送消息、历史记录、处理状态
        - **评价管理子界面**：查看用户评价、回复评价、统计分析
      - **扫码呼叫服务界面**：用户扫码后的服务选择和呼叫界面
        - **服务选择区域**：展示可选的服务项目（点餐、加水、结账、评价等）
        - **备注输入功能**：用户可添加特殊需求或备注信息
        - **呼叫确认功能**：确认选择的服务并发送呼叫请求
        - **直接评价功能**：用户可直接对服务进行评价而无需呼叫
        - **成功反馈界面**：呼叫成功后的确认提示和状态显示
      - **用户评价界面**：对商家/服务的评价功能
      - **举报界面**：举报不当内容的功能
      - **系统设置界面**：通知设置、隐私设置等
      - **隐私政策界面**：隐私政策条款展示
  - **6.1.2 管理端必需界面清单**
    - **登录界面**：管理员账号密码登录
    - **仪表板界面**：核心数据统计展示
    - **用户管理界面**：用户信息管理、状态控制
    - **内容审核界面**：待审核内容处理
    - **举报处理界面**：用户举报内容处理
    - **呼叫服务数据统计界面**：呼叫服务专项数据分析（仅平台PC管理端可见）
      - **核心指标概览**：总呼叫次数、活跃商家数、平均满意度、引流用户数等关键指标
      - **引流数据分析**：统计呼叫服务为平台带来的用户流量和活跃度趋势
      - **功能使用统计**：分析各类服务的使用频次和用户行为模式
      - **商家参与度分析**：活跃商家排行榜和参与度统计
      - **用户满意度趋势**：评价数据汇总和满意度变化趋势
      - **市场潜力分析**：基于使用数据评估功能的市场需求和发展潜力
      - **地域分布统计**：不同地区呼叫服务的使用情况和普及程度
    - **系统设置界面**：平台参数配置
  - **6.1.3 界面设计强制要求**
    - **完整性要求**：设计师必须为PRD中定义的所有功能提供对应界面
    - **一致性要求**：所有界面必须遵循统一的设计规范和交互模式
    - **平台适配要求**：所有界面必须符合微信小程序设计规范
    - **功能覆盖要求**：不得遗漏PRD中明确定义的任何功能界面

 - **6.1.4 设计交付标准与质量要求**
   - **交付文件格式要求**
     - **禁止HTML/CSS/JS文件**：设计师严禁提交任何HTML、CSS、JavaScript文件
     - **原型工具限制**：仅允许使用支持微信小程序规范的原型设计工具（如Figma、Sketch、Adobe XD等）
     - **设计稿格式**：交付高保真设计稿（.fig、.sketch、.xd等源文件）和导出的PNG/JPG预览图
     - **标注文档**：必须提供详细的设计标注文档，包含尺寸、颜色、字体、间距等规范
   - **设计内容审查标准**
     - **平台定位检查**：设计必须体现信息匹配平台特性，严禁电商化设计风格
     - **功能范围检查**：严格按照PRD功能清单设计，不得添加未定义功能
     - **商家端限制检查**：禁止设计独立的商家管理后台，所有商家功能必须集成在用户个人中心
     - **技术规范检查**：设计必须符合微信小程序组件库和WXSS样式规范
   - **设计一致性要求**
     - **视觉风格统一**：所有界面必须保持一致的视觉风格和交互模式
     - **组件复用**：优先使用微信小程序官方组件，确保用户体验一致性
     - **响应式适配**：设计必须考虑不同屏幕尺寸的适配方案
   - **质量验收标准**
     - **完整性验收**：所有PRD定义的界面必须有对应设计稿
     - **准确性验收**：设计内容必须与PRD功能描述完全一致
     - **可实现性验收**：设计方案必须在微信小程序技术框架内可实现
     - **用户体验验收**：界面设计必须符合微信小程序用户使用习惯
   - **不合规设计处理机制**
     - **发现违规设计**：立即要求设计师删除不合规文件
     - **重新设计要求**：违规设计必须按照PRD要求重新设计
     - **质量把关**：不合规设计不得进入开发阶段
     - **培训机制**：对设计师进行PRD理解和平台定位培训

 - **6.2 核心用户旅程地图设计**
  ```mermaid
  journey
      title 本地助手用户核心旅程
      section 新用户注册
        打开小程序: 5: 用户
        微信授权登录: 4: 用户
        位置授权: 3: 用户
        直接进入商铺板块: 5: 用户
        商铺板块默认高亮: 5: 用户
        全部分类默认选中: 5: 用户
      section 信息查找
        浏览商铺内容或切换板块: 5: 用户
        筛选分类: 4: 用户
        浏览信息列表: 5: 用户
        查看详情: 5: 用户
        联系发布者: 4: 用户
      section 信息发布
        点击发布按钮: 5: 用户
        选择发布类型: 4: 用户
        填写信息内容: 3: 用户
        手机验证: 2: 用户
        等待审核: 2: 用户
        信息上线: 5: 用户
      section 商家服务
        创建店铺信息: 3: 商家
        上传商品/服务: 4: 商家
        发布优惠券: 5: 商家
        核销券码: 4: 商家
        查看评价: 5: 商家
      section 呼叫服务
        商家生成服务二维码: 4: 商家
        用户扫码进入呼叫界面: 5: 用户
        选择服务内容并呼叫: 5: 用户
        商家接收呼叫推送: 4: 商家
        商家处理服务请求: 4: 商家
        用户评价服务质量: 5: 用户
  ```

- **6.3 关键业务流程与状态转换详述**
  - **6.3.1 信息发布流程**
    ```mermaid
    flowchart TD
        A[用户点击发布] --> B[选择板块类型]
        B --> C[填写基础信息]
        C --> D[上传图片/附件]
        D --> E[位置选择]
        E --> F[手机验证码验证]
        F --> G{验证成功?}
        G -->|否| F
        G -->|是| H[提交审核]
        H --> I[管理员审核]
        I --> J{审核结果}
        J -->|通过| K[信息上线展示]
        J -->|拒绝| L[通知用户修改]
        L --> C
        K --> M[用户可管理信息]
    ```

  - **6.3.2 优惠券核销流程**
    ```mermaid
    flowchart TD
        A[用户浏览商家信息] --> B[领取优惠券]
        B --> C[券存入个人中心]
        C --> D[用户到店消费]
        D --> E[出示券码给商家]
        E --> F[商家扫码/输入券码]
        F --> G{券码有效?}
        G -->|否| H[提示券码无效]
        G -->|是| I[核销成功]
        I --> J[用户可评价]
        J --> K[评价展示在商家页面]
    ```

  - **6.3.3 跑腿服务流程**
    ```mermaid
    flowchart TD
        A[用户发布跑腿需求] --> B[需求审核通过]
        B --> C[需求展示在跑腿板块]
        C --> D[服务者浏览需求]
        D --> E[服务者联系需求方]
        E --> F[双方协商服务细节]
        F --> G{达成一致?}
        G -->|否| H[继续寻找其他服务者]
        G -->|是| I[线下完成服务]
        I --> J[服务完成确认]
        J --> K[双方可互相评价]
    ```

  - **6.3.4 举报处理流程**
    ```mermaid
    flowchart TD
        A[用户发起举报] --> B[选择举报类型]
        B --> C[填写举报详情]
        C --> D[提交举报]
        D --> E[管理员接收举报]
        E --> F[管理员调查核实]
        F --> G{举报属实?}
        G -->|否| H[驳回举报]
        G -->|是| I[对违规内容处理]
        I --> J[通知举报者处理结果]
        I --> K[通知被举报者]
        K --> L{严重违规?}
        L -->|是| M[账号封禁]

  - **6.3.5 呼叫服务流程**
    ```mermaid
    flowchart TD
        A[商家创建服务二维码] --> B[配置服务场景信息]
        B --> C[设置服务内容和评价接收人]
        C --> D[生成防拍照二维码]
        D --> E[下载打印二维码和安全提示]
        E --> F[用户扫码进入服务界面]
        F --> G{二维码有效?}
        G -->|否| H[提示二维码无效]
        G -->|是| I[显示服务选项和评价入口]
        I --> J{用户选择}
        J -->|呼叫服务| K[选择服务内容]
        J -->|直接评价| L[进入评价界面]
        K --> M[添加备注信息]
        M --> N[确认发起呼叫]
        N --> O[推送消息到商家端]
        O --> P{商家操作}
        P -->|点击确认| Q[标记已处理]
        P -->|等待| R[3分钟后自动关闭]
        Q --> S[服务完成]
        R --> S
        L --> T[提交评价]
        S --> T
        T --> U{评价接收人}
        U -->|默认| V[服务人员接收评价]
        U -->|指定接收人| W[经理等接收人接收评价]
        V --> X[评价记录保存]
        W --> X
    ```

  - **6.3.6 二维码安全验证流程**
    ```mermaid
    flowchart TD
        A[用户扫描二维码] --> B[解析二维码内容]
        B --> C[验证平台标识]
        C --> D{是否平台二维码?}
        D -->|否| E[提示无效二维码]
        D -->|是| F[验证防拍照签名]
        F --> G{签名有效?}
        G -->|否| H[提示二维码已失效]
        G -->|是| I[验证时效性]
        I --> J{是否在有效期内?}
        J -->|否| K[提示二维码过期]
        J -->|是| L[验证使用次数]
        L --> M{是否超出使用限制?}
        M -->|是| N[提示使用次数已满]
        M -->|否| O[验证商家身份]
        O --> P{商家信息有效?}
        P -->|否| Q[提示商家信息异常]
        P -->|是| R[进入呼叫服务界面]
    ```
        L -->|否| N[警告处理]
    ```
- **6.3 多端界面设计规范与一致性要求**
  - **用户端界面设计要求与原型说明**
    - **登录界面设计**：
       - **界面布局**：
         - 顶部：应用Logo和名称
         - 中部：登录方式选择区域
           - 手机号登录：手机号输入框 + 验证码输入框 + 获取验证码按钮 + 登录按钮
           - 微信直接登录：微信授权登录按钮
         - 底部：扫码按钮（新增功能）

      - **登录方式设计**：
         - **手机号验证码登录**：
           - 输入11位手机号码
           - 点击"获取验证码"按钮，系统发送6位数字验证码
           - 输入验证码完成登录（即注册）
           - 验证码有效期5分钟，60秒内不可重复获取
           - **登录即注册**：首次使用手机号登录的用户自动完成注册，无需额外注册步骤
           - **商家认领检测**：登录成功后系统自动检测该手机号是否匹配已采集的商家数据
           - **认领提示逻辑**：
             - 若匹配到商家数据，显示认领提示："我们发现您可能是 [店铺名称] 的商家，是否认领管理？"
             - 提供三个选项：[是的，这是我的店铺] [不是我的店铺] [稍后处理]
             - 若未匹配到商家数据，直接进入正常使用流程，无认领提示
         - **微信直接登录**：
           - 点击微信授权登录按钮
           - 调用微信授权接口完成登录（即注册）
           - **登录即注册**：首次使用微信登录的用户自动完成注册，无需额外注册步骤
           - **手机号绑定**：微信登录后需要绑定手机号（通过验证码验证）
           - **商家认领检测**：绑定手机号后系统自动检测该手机号是否匹配已采集的商家数据
           - **认领提示逻辑**：与手机号登录的认领逻辑相同
       - **交互流程**：
         - 用户打开小程序 → 显示登录界面
         - 用户可选择：1）手机号验证码登录 2）微信授权登录 3）点击扫码按钮扫描平台服务二维码
         - 手机号登录路径：输入手机号 → 获取验证码 → 输入验证码 → 登录成功
         - 微信登录路径：点击微信登录 → 微信授权 → 登录成功
         - 扫码路径：点击扫码按钮 → 打开相机扫码界面 → 识别平台生成的呼叫服务二维码 → 进入对应服务界面（注：此功能非登录功能，而是用于扫描平台生成的呼叫服务二维码进行防伪验证和快速服务调用）
    - **主界面设计（登录后默认界面）**：
      - **登录后直接跳转逻辑**：
        - **取消首页概念**：完全取消传统意义的首页界面，用户登录后无需经过任何中间页面
        - **直接进入商铺板块**：用户完成微信授权登录和位置授权后，直接跳转到商铺板块界面
        - **商铺板块默认高亮**：商铺板块导航按钮处于选中高亮状态，用户一进入就能看到商铺内容
        - **全部分类默认选中**：商铺板块下的"全部"分类默认高亮选择状态
        - **无中间步骤**：彻底避免任何板块选择的中间界面，用户可直接在四大板块间自由切换
        - **取消独立发布入口**：不设置独立的发布主界面，采用弹窗选择板块的方式进行发布操作
      - **界面整体结构布局**：
        - **顶部搜索区域**：
          - **城市选择按钮**：位于搜索栏左侧独立位置，支持手动切换城市/区域
          - **搜索输入框**：位于城市按钮右侧，支持全局关键词搜索
          - **布局方式**：水平排列 `[城市按钮] [搜索输入框]`，搜索框占据剩余空间
        - **四大板块导航区域**：
          - **导航按钮**：商铺、信息、出行、跑腿四个板块按钮，水平排列
          - **默认状态**：商铺板块高亮选中，其他板块为未选中状态
          - **交互方式**：点击任意板块按钮直接切换到对应板块内容
          - **视觉设计**：选中板块有明显的高亮效果（如背景色变化、文字加粗等）
        - **板块分类筛选区域**：
          - **显示逻辑**：根据当前选中的板块显示对应的分类选项
          - **商铺板块分类**：全部、实体店铺、在线网店、流动摊贩、上门师傅
          - **信息板块分类**：全部、招聘、出租、求租、出售、闲置、求购、求职、其他
          - **出行板块分类**：全部、车找人、人找车、货找车、车找货、代驾服务
          - **跑腿板块分类**：全部、代买代购、代送服务、代办事务、其他跑腿
          - **排列方式**：水平滚动列表，支持左右滑动查看更多分类
          - **默认状态**："全部"分类为默认选中状态（高亮显示）
        - **内容列表区域**：
          - **显示内容**：根据当前选中的板块和分类，以卡片式布局展示对应的信息列表
          - **卡片设计**：统一的卡片样式，包含核心信息和交互按钮
          - **加载方式**：支持下拉刷新和上拉加载更多
      - **用户交互流程**：
        - 登录完成 → 直接显示商铺板块"全部"分类内容
        - 用户可点击其他板块按钮直接切换板块
        - 用户可点击分类选项筛选当前板块下的内容
        - 用户可使用顶部搜索框进行全局搜索
        - **彻底取消首页界面**：登录后不再有传统意义的首页
        - **避免任何中间选择步骤**：无需用户进行板块选择操作
    - **底部导航栏设计**：
      - **导航结构**：商铺、发布、消息、我的四个主要入口
      - **发布功能交互**：
        - 点击"发布"按钮后，弹出板块选择模态窗口
        - 模态窗口包含：商铺、信息、出行三个发布选项（跑腿板块暂不开放发布）
        - 用户选择对应板块后，跳转到该板块的发布表单页面
        - **优势**：简化操作流程，避免复杂的发布主界面设计，用户可快速选择发布类型
    - **商铺卡片核心信息定义**：
      - **1. 商铺主图/Logo**：
        - 优先级：高
        - 说明：展示商家最具代表性的图片或Logo，主要用于品牌识别。
        - 尺寸建议：**建议采用类似头像的较小尺寸（例如1:1比例，如40x40px），不宜过大，以突出商铺名称和经营范围等核心文字信息。**
      - **2. 商铺名称**：
        - 优先级：高
        - 说明：清晰、完整地显示商家官方认证名称。
      - **3. 认证标签**：
        - 优先级：高
        - 说明：显示商家已通过的认证类型，如“已认证”、“品牌商家”等，提升信任度。
      - **4. 评分/评价数量**：
        - 优先级：中
        - 说明：展示用户综合评分（如星级）和总评价数量，帮助用户判断口碑。此数据来源于用户评价系统，用户仅在通过优惠券/体验券完成消费并核销后方可进行评价。
        - 数据来源：用户评价系统。
      - **5. 主要经营范围 (用户自定义)**：
        - 优先级：中
        - 说明：商家手动输入的经营范围或特色描述，文字不宜过长，突出核心业务。
        - 示例：“川菜、家常菜、免费WiFi”、“专业美发、烫染”
      - **6. 距离**：
        - 优先级：中（视用户位置授权情况）
        - 说明：显示商家与用户当前位置的直线距离，例如“500m内”、“1.2km”。
      - **7. 优惠券/体验券标签**：
        - 优先级：高（若商家已开通）
        - 说明：醒目提示商家当前提供的优惠券或体验券（例如通过不同颜色或微小图标区分不同类型，如满减券、折扣券、体验券本身）。**仅当商家开通并设置了有效的优惠券/体验券时，此标签才显示。** 常见的标签文字可为“领券”、“体验券”、“首单优惠”等，旨在吸引用户点击。
        - 交互：用户点击此标签后，应**弹出一个模态窗口（小窗口）**，清晰展示该优惠券/体验券的详细内容（如使用条件、有效期、优惠额度/内容）和“**立即领取**”或“**查看详情**”按钮。若商家有多种券，弹窗内可列表展示供用户选择。
      - **8. 营业状态/时间**：
        - 优先级：中
        - 说明：显示商家当前的营业状态（如“营业中”、“休息中”）或主要营业时间段。
      - **9. 商铺类型标签**：
        - 优先级：中
        - 说明：根据商家在后台选择的店铺类型（如：实体店铺、在线网店、流动摊贩、上门师傅），在卡片上通过小图标或文字标签清晰展示其主要服务模式。例如，实体店显示“实体”，网店显示“网店”，流动摊贩“摊贩”，上门师傅“师傅”。
        - 交互：此标签为静态展示，主要用于信息传达。
      - **10. 电话按钮**：
        - 优先级：高
        - 说明：在卡片上提供一个明显的电话图标或按钮，用户点击后可直接调用系统的拨号功能，联系商家（商家电话号码需做脱敏处理，例如显示为“联系商家”或仅图标，点击后才完整显示或直接拨打）。
        - 交互：点击后触发拨打电话动作。
      - **11. 产品/服务缩略图展示**：
        - 优先级：中
        - 说明：在卡片下方（或合适位置）横向展示商家主要产品或服务的缩略图（建议3-4张）。用户点击缩略图可直接跳转至对应的产品/服务详情页。若商家发布的产品/服务少于建议数量，则有几张显示几张。此设计旨在提升产品曝光和用户转化。
        - 图片来源：商家产品列表库中已上传的产品/服务图片。
        - 交互：用户点击任一缩略图后，应跳转至该**商家的产品/服务列表页**，并可考虑将视图定位到或高亮显示被点击的产品/服务（若技术上易于实现）。
      - **卡片布局建议**：
        - 左侧为商铺主图/Logo，右侧为文字信息区域。
        - 文字信息区域从上至下依次为：商铺名称、认证标签与评分/评价、经营范围、距离与优惠券/营业状态。
        - 整体风格简洁、信息层级清晰，符合产品视觉规范。
    - **详情页**：展示完整信息，提供联系方式（脱敏，点击后可复制或直接拨打）、举报入口等。
  - **管理端界面设计要求与原型说明**
    - 简洁、高效、易用，注重数据展示和管理操作的便捷性。
- **6.4 交互设计原则与可用性标准**
  - 操作流程清晰简短。
  - 反馈及时明确。
  - 遵循微信小程序设计规范。
  - **UI/UX设计师工作要求**：
    - **严格遵循板块分类规范**：设计师必须严格按照PRD文档要求的四大板块（商铺、出行、信息、跑腿）及其对应分类进行设计
    - **禁止擅自更改**：绝对不允许擅自更改或调整板块分类结构
    - **变更流程**：如需更改板块或分类，必须先向产品经理提出申请，经产品经理确认后方可修改
    - **设计一致性**：确保各板块和分类的设计风格保持一致，符合整体产品设计规范
- **6.5 视觉设计风格指导**
  - **整体风格**：高端、简洁、现代化、易用性优先，融入科技感元素
  - **科技感设计要求**：
    - 采用现代化的渐变色彩和光效设计
    - 使用几何图形和线条元素增强科技感
    - 界面动效流畅，具有未来感的交互体验
    - 图标设计简洁现代，符合科技产品审美
  - **色彩搭配**：以蓝色、白色、灰色为主色调，辅以科技感的渐变效果
  - **四大板块颜色配置**（2024年1月更新）：
    - **商铺板块**：主色系为蓝色（`bg-blue-600`、`text-blue-400`），体现商业专业性
    - **信息板块**：主色系为紫色（`bg-purple-600`、`text-purple-400`），突出信息发布特性
    - **出行板块**：主色系为橙色（`bg-orange-600`、`text-orange-400`），传达出行活力感
    - **跑腿板块**：主色系为青色（`bg-teal-600`、`text-teal-400`），区别于其他板块的独特色系
    - **颜色应用范围**：顶部导航栏激活状态、底部导航栏激活状态、相关功能按钮和标签
  - **字体选择**：现代化无衬线字体，保证可读性的同时体现科技感
  - **布局设计**：简洁明了的卡片式布局，合理的留白和层次感
  - **非电商化设计具体指导**：
    - **信息展示优先**：界面设计以信息展示和浏览为核心，突出内容的可读性和查找便利性
    - **去商业化元素**：
      - ❌ 禁止使用：购物车图标、价格标签、促销横幅、"立即购买"按钮
      - ❌ 禁止使用：订单状态、支付流程界面
      - ❌ 禁止使用：库存显示、销量统计、评分星级（商家评分除外）
      - ✅ 推荐使用：信息卡片、联系方式、位置信息、服务描述
    - **交互方式设计**：
      - 主要交互为"查看详情"、"联系商家"、"获取位置"、"分享信息"
      - 避免"加入购物车"、"立即下单"、"选择规格"等电商交互
      - 强调信息浏览、筛选、搜索、联系等功能
    - **视觉层次设计**：
      - 突出商家/服务提供者的联系信息和基本介绍
      - 弱化价格信息，如有价格仅作参考展示，不做重点突出
      - 重点展示服务内容、营业时间、地址位置等实用信息
  - **6.6 响应式设计与跨设备适配要求**
  - 主要针对微信小程序环境优化。

## 7. 非功能性需求规范

- **7.1 系统性能需求**
  - **7.1.1 响应时间要求**：页面加载 < 2s，API调用 < 500ms。
  - **7.1.2 并发用户数与系统吞吐量要求**：(初期根据预估用户量设定)
  - **7.1.3 系统可用性与稳定性指标**：99.9%在线率。
- **7.2 信息安全需求**
  - **7.2.1 数据加密标准与传输安全协议**：HTTPS。
  - **7.2.2 用户认证授权机制设计**：微信授权登录，身份证实名认证（部分功能前置）。
  - **7.2.3 隐私保护与数据合规要求**：电话号码脱敏处理（仅总管理员可查看完整号码），位置信息获取需用户授权。
  - **7.2.4 安全防护策略**：防SQL注入、XSS攻击、CSRF等常见攻击，敏感操作需验证码。
- **7.3 用户体验与可访问性标准**
  - **7.3.1 界面易用性与学习成本控制**：符合大众用户使用习惯。
  - **7.3.2 无障碍设计规范**：(暂不作强制要求，后续可优化)
  - **7.3.3 多语言支持与国际化要求**：仅支持中文。
- **7.4 法规合规性要求**
  - **7.4.1 数据保护法规遵循**：遵循《中华人民共和国个人信息保护法》。
  - **7.4.2 平台政策合规**：遵循微信小程序平台运营规范。
- **7.5 数据统计与商业智能需求**
  - **7.5.1 关键业务指标埋点设计**：用户注册数、日活/月活、各板块信息发布量、浏览量、点击量等。
  - **7.5.2 用户行为分析与转化漏斗追踪**：(待细化)
  - **7.5.3 实时监控与预警机制**：(针对服务器状态和关键错误)

## 8. 技术架构指导建议

- **8.1 推荐技术栈与架构模式**
  - **前端技术栈（强制要求）**：
    - **必须使用**：微信小程序原生开发（WXML、WXSS、JavaScript）
    - **严禁使用**：HTML、CSS、Tailwind CSS、Bootstrap等Web技术
    - **严禁使用**：Web端图标库（如Lucide Icons、Font Awesome等）
    - **图标要求**：使用微信小程序官方图标或符合小程序规范的图标
    - **备选方案**：Taro/uni-app等多端框架（需产品经理明确批准）
  - **后端**：Node.js/Java/Python/Go等，配合相应框架。
  - **数据库**：MySQL/PostgreSQL/MongoDB等。
  - **架构模式**：前后端分离，微服务架构（视复杂度而定）。
  - **技术栈验证要求**：
    - 设计师在开始设计前必须确认技术栈选择
    - 开发团队在开始开发前必须进行技术栈审查
    - 任何技术栈变更需要产品经理书面批准
  - **设计文件合规性要求**：
    - **禁止创建Web格式文件**：设计师不得创建.html、.css、.js等Web技术文件
    - **设计工具限制**：必须使用支持小程序规范的设计工具（Figma、Sketch等）
    - **组件库要求**：设计必须基于微信小程序官方组件库进行
    - **交付格式要求**：设计稿交付格式为.fig、.sketch或符合小程序规范的原型
    - **违规处理**：发现违规设计文件立即停止使用并要求重新设计
  - **商家端功能限制技术实现**：
    - **禁止开发独立商家端管理界面**：所有商家功能必须集成在用户端个人中心
    - **禁止复杂数据统计功能**：不得开发销售报表、流量分析等高级统计功能
    - **禁止高级营销工具**：除基础优惠券外，不得开发会员系统、积分系统等
    - **禁止订单管理系统**：不得开发完整的电商订单流程和管理功能
    - **功能范围技术约束**：开发团队必须严格按照PRD中"功能范围限制说明"进行开发
    - **设计合规性检查**：
      - 设计师不得创建独立的商家管理界面设计
      - 所有商家功能界面必须在用户个人中心框架内设计
      - 禁止设计复杂的数据图表、营销工具界面
      - 违规设计文件必须删除并重新设计
- **8.2 系统集成需求与API设计规范**
  - RESTful API风格。
- **8.3 数据库设计与数据模型建议**
  - **8.3.1 核心数据表结构**
    - **用户表 (users)**
      - id (主键), openid, phone, nickname, avatar, real_name, id_card, status, created_at, updated_at
    - **商铺表 (shops)**
      - id, user_id, name, avatar, phone, business_scope, operating_hours, location, certification_status, created_at, updated_at
    - **商品/服务表 (products)**
      - id, shop_id, category_id, name, images, description, price, unit, status, created_at, updated_at
    - **信息表 (information)**
      - id, user_id, type, title, content, images, price, location, phone, status, created_at, updated_at
    - **出行表 (transportation)**
      - id, user_id, type, title, departure, destination, departure_time, price, vehicle_info, description, phone, status, created_at, updated_at
    - **跑腿表 (errands)**
      - id, user_id, type, title, description, location, required_time, price, requirements, phone, status, created_at, updated_at
    - **优惠券表 (coupons)**
      - id, shop_id, title, description, discount_type, discount_value, valid_from, valid_to, total_count, used_count, status
    - **用户券表 (user_coupons)**
      - id, user_id, coupon_id, code, status, used_at, created_at
    - **评价表 (reviews)**
      - id, user_id, target_id, target_type, rating, content, images, created_at
    - **举报表 (reports)**
      - id, reporter_id, target_id, target_type, reason, description, status, handled_by, handled_at, created_at
  - **8.3.2 数据关系设计**
    - 用户与商铺：一对多关系
    - 商铺与商品/服务：一对多关系
    - 用户与各类信息发布：一对多关系
    - 优惠券与用户券：一对多关系
    - 评价支持多态关联（商铺、商品、服务等）
  - **8.3.3 索引优化建议**
    - 用户表：openid, phone 建立唯一索引
    - 各信息表：user_id, status, created_at 建立复合索引
    - 地理位置相关：使用空间索引优化距离查询

- **8.4 第三方服务集成要求与备选方案**
  - **8.4.1 地图服务**
    - **主选方案**：腾讯位置服务（与微信生态集成度高）
    - **备选方案**：高德地图API
    - **集成功能**：地址解析、距离计算、地图选点、路径规划
    - **位置服务管理**：地理位置服务由用户手机系统设置统一管理，应用遵循系统权限设置
  - **8.4.2 短信服务**
    - **主选方案**：腾讯云短信服务
    - **备选方案**：阿里云短信服务、华为云短信
    - **使用场景**：注册验证、发布验证、重要操作确认
  - **8.4.3 实名认证服务**
    - **主选方案**：腾讯云人脸核身
    - **备选方案**：阿里云实人认证
    - **认证等级**：身份证OCR + 人脸识别
  - **8.4.4 内容安全审核**
    - **主选方案**：腾讯云天御内容安全
    - **备选方案**：阿里云内容安全
    - **审核内容**：文本、图片违规检测
  - **8.4.5 云存储服务**
    - **主选方案**：腾讯云对象存储COS
    - **备选方案**：阿里云OSS
    - **存储内容**：用户上传图片、头像等
  - **8.4.6 推送服务**
    - **主选方案**：微信小程序订阅消息
    - **备选方案**：腾讯云移动推送
    - **推送场景**：审核结果、系统通知、互动消息

- **8.5 系统扩展性与维护性考量**
  - **8.5.1 架构扩展性**
    - 采用微服务架构，按业务模块拆分服务
    - 使用容器化部署，支持水平扩展
    - 数据库读写分离，支持分库分表
    - 引入缓存层（Redis）提升性能
  - **8.5.2 代码维护性**
    - 统一代码规范和开发标准
    - 完善的单元测试和集成测试
    - 详细的API文档和代码注释
    - 版本控制和代码审查机制
  - **8.5.3 运维监控**
    - 应用性能监控（APM）
    - 日志收集和分析系统
    - 自动化部署和回滚机制
    - 告警和故障处理流程

- **8.6 开发环境与部署架构建议**
  - **8.6.1 开发环境**
    - **前端开发**：微信开发者工具 + VS Code
    - **后端开发**：IntelliJ IDEA / VS Code
    - **数据库**：MySQL 8.0+
    - **版本控制**：Git + GitLab/GitHub
    - **项目管理**：JIRA / Tapd / 禅道
  - **8.6.2 测试环境**
    - 独立的测试服务器和数据库
    - 自动化测试工具集成
    - 测试数据管理和重置机制
  - **8.6.3 生产环境**
    - **云服务商**：腾讯云（推荐）/ 阿里云
    - **服务器配置**：
      - Web服务器：2核4G起步，支持弹性扩容
      - 数据库：4核8G，SSD存储
      - Redis缓存：2核4G
    - **负载均衡**：云负载均衡器
    - **CDN加速**：静态资源CDN分发
    - **安全防护**：Web应用防火墙、DDoS防护
  - **8.6.4 部署流程**
    - 基于Docker容器化部署
    - CI/CD自动化流水线
    - 蓝绿部署或滚动更新
    - 数据库迁移脚本管理

## 9. 质量保证与验收标准

- **9.0 设计审查与验收标准**
  - **9.0.1 设计完整性审查**
    - **界面覆盖率检查**：设计师必须为PRD中定义的所有功能模块提供对应界面设计
    - **必需界面清单验证**：严格按照6.0节"必需界面设计要求"进行逐项检查
    - **功能流程完整性**：确保用户可以通过界面完成PRD中定义的所有业务流程
    - **交互逻辑一致性**：验证界面间的跳转逻辑与PRD要求一致
  - **9.0.2 平台规范合规性审查**
    - **微信小程序规范**：所有界面必须符合微信小程序设计规范
    - **技术栈限制**：确保设计中不包含禁用的web技术元素
    - **组件使用规范**：仅使用微信小程序原生组件或经批准的组件库
  - **9.0.3 设计质量标准**
    - **视觉一致性**：色彩、字体、间距等视觉元素保持统一
    - **交互一致性**：相同功能在不同界面中的交互方式保持一致
    - **信息架构清晰**：界面信息层级清晰，用户认知负担最小
    - **易用性标准**：符合"高端大气、简洁明了、易学易上手"的设计要求
  - **9.0.4 设计审查流程**
    - **初审**：产品经理对设计稿进行PRD符合性检查
    - **技术审查**：开发团队对技术可行性进行评估
    - **用户体验审查**：UX团队对用户体验进行评估
    - **最终验收**：项目负责人进行最终设计验收
  - **9.0.5 设计缺陷处理**
    - **缺失界面**：设计师必须补充所有遗漏的必需界面
    - **不符合规范**：必须修改至完全符合平台规范要求
    - **功能不完整**：必须完善界面功能直至满足PRD要求
    - **重新设计要求**：严重不符合要求的设计必须重新设计

- **9.1 功能验收标准汇总矩阵**
  
  | 功能模块 | 验收标准 | 测试方法 | 责任人 |
  |---------|---------|---------|--------|
  | **用户注册登录** | 微信授权登录成功率>99%，手机验证码60s内送达 | 自动化测试+手工测试 | 测试工程师 |
  | **信息发布** | 发布成功率>98%，图片上传支持5MB以内，审核24h内完成 | 功能测试+性能测试 | 测试工程师 |
  | **搜索功能** | 搜索响应时间<500ms，搜索结果准确率>95% | 性能测试+准确性测试 | 测试工程师 |
  | **地理位置** | 定位精度误差<100m，距离计算准确率>98% | 实地测试+算法验证 | 测试工程师 |
  | **优惠券系统** | 券码生成唯一性100%，核销成功率>99% | 压力测试+业务测试 | 测试工程师 |
  | **消息推送** | 推送到达率>95%，推送延迟<30s | 第三方服务测试 | 测试工程师 |
  | **内容审核** | 违规内容识别率>90%，误判率<5% | 人工审核+AI审核 | 运营人员 |
  | **数据统计** | 数据准确性100%，报表生成时间<10s | 数据一致性测试 | 测试工程师 |

- **9.2 性能基准与压力测试要求**
  - **9.2.1 响应时间要求**
    - 页面首屏加载时间：<2秒
    - API接口响应时间：<500ms
    - 图片加载时间：<3秒
    - 搜索响应时间：<500ms
  - **9.2.2 并发性能要求**
    - 支持1000并发用户同时在线
    - 支持100并发用户同时发布信息
    - 数据库连接池支持200个并发连接
  - **9.2.3 压力测试场景**
    - **场景1**：模拟1000用户同时浏览首页
    - **场景2**：模拟100用户同时发布信息
    - **场景3**：模拟500用户同时搜索
    - **场景4**：模拟200商家同时核销优惠券
  - **9.2.4 性能监控指标**
    - CPU使用率<80%
    - 内存使用率<85%
    - 数据库连接数<最大连接数的80%
    - 磁盘I/O<80%

- **9.3 代码质量标准与测试覆盖率要求**
  - **9.3.1 代码质量标准**
    - **代码规范**：严格遵循团队制定的编码规范
    - **代码复杂度**：圈复杂度<10
    - **代码重复率**：<5%
    - **代码注释率**：>30%
    - **代码审查**：所有代码必须经过Code Review
  - **9.3.2 测试覆盖率要求**
    - **单元测试覆盖率**：>80%
    - **接口测试覆盖率**：>90%
    - **功能测试覆盖率**：100%
    - **兼容性测试**：覆盖主流微信版本
  - **9.3.3 安全测试要求**
    - SQL注入测试：100%通过
    - XSS攻击测试：100%通过
    - CSRF攻击测试：100%通过
    - 敏感信息泄露测试：100%通过

- **9.4 用户验收测试（UAT）计划**
  - **9.4.1 UAT测试范围**
    - 核心业务流程端到端测试
    - 用户界面易用性测试
    - 跨平台兼容性测试
    - 业务规则正确性验证
  - **9.4.2 UAT测试环境**
    - 独立的UAT测试环境
    - 与生产环境配置一致
    - 真实的测试数据
  - **9.4.3 UAT测试用例**
    - **用户注册登录流程**：新用户注册、老用户登录、权限验证
    - **信息发布流程**：各板块信息发布、编辑、删除
    - **信息查找流程**：搜索、筛选、查看详情、联系发布者
    - **商家服务流程**：店铺创建、商品管理、优惠券发布、核销
    - **跑腿服务流程**：需求发布、信息展示、线下联系
    - **管理后台流程**：内容审核、用户管理、数据统计
  - **9.4.4 UAT验收标准**
    - 所有核心功能正常运行
    - 用户体验满足设计要求
    - 性能指标达到预期
    - 无阻塞性缺陷

- **9.5 上线前检查清单与发布标准**
  - **9.5.1 功能完整性检查**
    - [ ] 所有计划功能开发完成
    - [ ] 功能测试100%通过
    - [ ] 用户验收测试通过
    - [ ] 已知缺陷修复完成
  - **9.5.2 性能与安全检查**
    - [ ] 性能测试达标
    - [ ] 安全测试通过
    - [ ] 第三方服务集成测试通过
    - [ ] 数据备份恢复测试通过
  - **9.5.3 运维准备检查**
    - [ ] 生产环境部署完成
    - [ ] 监控告警配置完成
    - [ ] 日志收集配置完成
    - [ ] 应急预案制定完成
  - **9.5.4 合规性检查**
    - [ ] 微信小程序审核通过
    - [ ] 隐私政策和用户协议完善
    - [ ] 数据安全合规检查通过
    - [ ] 内容审核机制运行正常
  - **9.5.5 发布标准**
    - **发布条件**：所有检查项目100%通过
    - **发布流程**：测试环境→预发布环境→生产环境
    - **回滚准备**：数据库备份、代码版本标记、快速回滚方案
    - **发布监控**：实时监控关键指标，异常情况立即处理

## 10. 产品成功评估体系

- **10.1 关键绩效指标（KPIs）定义与目标设定**
  - **10.1.1 用户增长指标**
    - **新用户注册数**：目标每月新增1000+用户（MVP阶段）
    - **月活跃用户数（MAU）**：目标6个月内达到5000+活跃用户
    - **日活跃用户数（DAU）**：目标DAU/MAU比例达到20%以上
    - **用户留存率**：次日留存≥40%，7日留存≥25%，30日留存≥15%
    - **用户获取成本（CAC）**：通过自然增长和口碑传播，控制获客成本
  - **10.1.2 内容质量指标**
    - **信息发布数量**：目标每日新增信息50+条
    - **信息质量评分**：用户评分平均4.0分以上（5分制）
    - **信息有效性**：信息被查看/联系的转化率≥15%
    - **内容审核效率**：自动审核通过率≥80%，人工审核响应时间≤2小时
    - **违规内容比例**：违规内容占比≤2%
  - **10.1.3 用户参与指标**
    - **用户互动率**：点击、收藏、分享等互动行为占比≥30%
    - **搜索成功率**：用户搜索后进行后续操作的比例≥60%
    - **发布转化率**：浏览用户中进行信息发布的比例≥10%
    - **平均使用时长**：单次使用时长≥8分钟
    - **功能使用深度**：使用3个以上功能模块的用户占比≥40%
  - **10.1.4 技术性能指标**
    - **系统可用性**：服务可用率≥99.5%
    - **页面加载速度**：首屏加载时间≤3秒
    - **API响应时间**：平均响应时间≤500ms
    - **错误率**：系统错误率≤0.1%
    - **并发处理能力**：支持1000+并发用户访问

- **10.2 北极星指标选择与价值关联分析**
  - **10.2.1 北极星指标定义**
    - **主指标**：月活跃用户数（MAU）
    - **辅助指标**：用户月均使用频次、信息发布成功率
  - **10.2.2 价值关联分析**
    - **用户价值体现**：MAU增长代表产品解决了用户真实需求
    - **商业价值潜力**：活跃用户基数是未来商业化的基础
    - **产品健康度**：持续的MAU增长反映产品生命力
    - **市场认可度**：MAU增长速度体现市场对产品的接受程度
  - **10.2.3 指标分解与驱动因素**
    - **新用户获取**：通过口碑传播、社交分享获得新用户
    - **用户激活**：新用户完成首次信息发布或成功找到所需信息
    - **用户留存**：通过持续的价值提供保持用户活跃
    - **用户推荐**：满意用户向朋友推荐产品

- **10.3 HEART指标体系（Google用户体验评估框架）**
  - **10.3.1 Happiness（用户满意度）**
    - **测量方法**：应用内评分、用户反馈调研、NPS净推荐值
    - **目标值**：应用商店评分≥4.5分，NPS≥50
    - **关键问题**："您会向朋友推荐这个产品吗？"
    - **改进策略**：定期收集用户反馈，快速响应用户需求
  - **10.3.2 Engagement（用户参与度）**
    - **测量指标**：DAU、会话时长、页面浏览深度、功能使用频率
    - **目标值**：平均会话时长≥8分钟，页面浏览深度≥3页
    - **细分维度**：按用户类型、功能模块、时间段分析
    - **提升策略**：优化内容推荐算法，增加用户互动功能
  - **10.3.3 Adoption（功能采用率）**
    - **测量范围**：新功能使用率、核心功能渗透率、高级功能采用率
    - **目标值**：核心功能使用率≥80%，新功能7天采用率≥30%
    - **分析维度**：功能发现路径、使用障碍、用户教育效果
    - **优化方向**：改进功能引导，降低使用门槛
  - **10.3.4 Retention（用户留存率）**
    - **关键指标**：次日留存、7日留存、30日留存、90日留存
    - **目标值**：次日留存≥40%，7日留存≥25%，30日留存≥15%
    - **流失分析**：流失用户行为路径、流失原因调研
    - **留存策略**：个性化推送、用户召回、价值提醒
  - **10.3.5 Task Success（任务完成率）**
    - **核心任务**：信息搜索、信息发布、商家查找、联系建立
    - **成功定义**：用户完成预期操作并获得满意结果
    - **测量方法**：漏斗分析、用户路径追踪、任务完成时间
    - **优化重点**：简化操作流程，提高信息匹配准确性

- **10.4 商业指标与用户价值指标平衡**
  - **10.4.1 阶段性指标重点**
    - **MVP阶段（0-6个月）**：专注用户价值指标，建立用户基础
    - **成长阶段（6-18个月）**：平衡用户价值与商业探索
    - **成熟阶段（18个月+）**：商业指标与用户价值并重
  - **10.4.2 价值平衡原则**
    - **用户价值优先**：确保商业化不损害核心用户体验
    - **长期价值导向**：避免短期商业利益损害长期用户关系
    - **透明度原则**：商业化功能对用户保持透明和可选择
  - **10.4.3 潜在商业指标设计**
    - **信息推广收入**：商家信息置顶、推荐位收费
    - **增值服务收入**：认证服务、高级功能订阅
    - **广告收入**：精准本地广告投放（严格控制数量和质量）

- **10.5 数据收集与分析报告机制**
  - **10.5.1 数据埋点策略**
    - **用户行为埋点**：页面访问、按钮点击、搜索行为、停留时间
    - **业务流程埋点**：信息发布流程、商家查找流程、联系建立流程
    - **性能监控埋点**：页面加载时间、接口响应时间、错误率统计
    - **转化漏斗埋点**：关键业务路径的各个转化节点
  - **10.5.2 实时监控与预警**
    - **关键指标监控**：DAU、MAU、留存率、任务完成率实时监控
    - **异常预警机制**：指标异常波动自动预警通知
    - **性能监控**：系统性能、接口可用性、错误率监控
    - **用户反馈监控**：负面评价、投诉举报实时跟踪
  - **10.5.3 定期分析报告**
    - **周报**：核心指标趋势、异常事件分析、用户反馈汇总
    - **月报**：深度数据分析、用户行为洞察、产品优化建议
    - **季报**：产品发展评估、市场竞争分析、战略调整建议
    - **专项报告**：新功能效果评估、用户调研分析、A/B测试结果

---

## **附录A：设计师工作指导原则**

### **A.1 产品定位理解要求**
- **核心定位**：本产品是信息匹配平台，不是电商平台
- **主要价值**：帮助用户快速找到本地商家和服务信息
- **设计理念**：简洁、高效、本地化、信息导向
- **避免误区**：不要设计成购物平台、不要添加复杂的商业功能

### **A.2 技术平台限制说明**
- **开发平台**：微信小程序原生开发
- **禁用技术**：HTML、CSS、JavaScript网页技术
- **组件库**：必须使用微信小程序官方组件
- **样式规范**：使用WXSS样式语言，不是CSS

### **A.3 商家端功能设计限制**
- **严禁独立后台**：不得设计独立的商家管理系统
- **集成原则**：所有商家功能必须集成在用户个人中心
- **功能限制**：不得设计复杂的数据统计、营销工具、订单管理
- **简化原则**：商家功能保持简单、直观、易用

### **A.4 界面设计标准**
- **视觉风格**：现代简约、清新自然、符合微信生态
- **色彩搭配**：主色调建议使用微信绿或清新蓝绿色系
- **字体规范**：使用微信小程序默认字体，确保跨平台一致性
- **布局原则**：信息层次清晰、操作路径简单、减少用户认知负担

### **A.5 用户体验设计要求**
- **操作简化**：最多3步完成核心任务
- **信息优先**：突出展示关键信息，弱化装饰元素
- **本地化体验**：强调地理位置、距离、本地特色
- **快速响应**：界面反馈及时、加载状态清晰

---

## **附录B：常见设计问题与解决方案**

### **B.1 商家端设计常见错误**
- **错误**：设计独立的商家管理后台
- **正确**：在用户个人中心添加"商家模式"切换
- **错误**：设计复杂的数据分析图表
- **正确**：提供简单的信息发布和管理功能
- **错误**：设计订单管理系统
- **正确**：设计信息发布和联系方式管理

### **B.2 平台定位设计常见错误**
- **错误**：设计购物车、支付流程
- **正确**：设计信息展示和联系建立
- **错误**：设计商品详情页
- **正确**：设计商家信息详情页
- **错误**：设计交易评价系统
- **正确**：设计服务体验评价系统

### **B.3 技术实现设计常见错误**
- **错误**：使用HTML文件格式
- **正确**：使用设计工具原生格式（.fig、.sketch等）
- **错误**：设计复杂的CSS动画
- **正确**：使用微信小程序支持的简单动画
- **错误**：设计不符合小程序规范的交互
- **正确**：遵循微信小程序交互规范

### **B.4 设计交付常见问题**
- **问题**：设计稿不完整，缺少关键界面
- **解决**：严格按照PRD界面清单检查设计完整性
- **问题**：设计风格不统一
- **解决**：建立设计规范文档，确保一致性
- **问题**：设计不符合微信小程序规范
- **解决**：深入学习微信小程序设计指南

---

## **附录C：PRD文档维护说明**

### **C.1 文档版本管理**
- **版本号规则**：主版本.次版本.修订版本（如1.2.3）
- **更新频率**：根据产品迭代需求和市场反馈及时更新
- **变更记录**：详细记录每次修改的内容、原因、影响范围
- **审批流程**：重大变更需要产品、技术、设计团队共同评审

### **C.2 文档使用指南**
- **阅读顺序**：建议按章节顺序完整阅读，理解产品全貌
- **重点关注**：各角色重点关注与自己工作相关的章节
- **疑问处理**：对PRD内容有疑问时，及时与产品经理沟通确认
- **反馈机制**：发现问题或改进建议，通过正式渠道反馈

### **C.3 跨团队协作**
- **设计团队**：重点关注第6章用户体验设计和附录A设计指导
- **开发团队**：重点关注第7-8章技术架构和功能需求
- **测试团队**：重点关注第9章质量保证和验收标准
- **运营团队**：重点关注第10章产品评估指标和商业模式

---

## **附录D：界面交互优化更新记录**

### **D.1 计费方式动态化优化 (2024年12月更新)**
#### **优化内容**
- **问题描述**：`publish-transportation.html`页面中计费方式选项固定，未根据出行类型动态调整
- **解决方案**：
  - 人找车：显示"面议"、"每人"选项
  - 车找人：显示"面议"、"空位"选项  
  - 货找车：显示"面议"、"运费"选项
  - 车找货：显示"面议"、"每趟"选项
  - 代驾：显示"面议"、"每公里"选项
  - 默认选中"面议"选项
- **技术实现**：添加`updatePriceTypeOptions`函数，根据出行类型动态更新计费方式选项

### **D.2 四大板块导航交互修复 (2024年12月更新)**
#### **修复内容**
- **问题描述**：四大板块（商铺、信息、出行、跑腿）导航按钮点击无响应
- **解决方案**：为所有板块导航按钮添加`onclick`事件，实现页面跳转功能
- **涉及页面**：
  - `errands.html`：添加商铺、信息、出行页面跳转
  - `information.html`：添加商铺、出行、跑腿页面跳转
  - `transportation.html`：添加商铺、信息、跑腿页面跳转
  - `shops.html`：添加信息、出行、跑腿页面跳转

### **D.3 分类筛选交互优化 (2024年12月更新)**
#### **优化内容**
- **问题描述**：各板块分类筛选按钮点击无响应，颜色不统一
- **解决方案**：
  - **跑腿板块**：使用teal色系（`bg-teal-600`）作为选中状态
  - **信息板块**：使用紫色系（`bg-purple-600`）作为选中状态
  - **出行板块**：使用橙色系（`bg-orange-600`）作为选中状态
  - **商铺板块**：使用蓝色系（`bg-blue-600`）作为选中状态
- **交互逻辑**：点击分类按钮时，重置所有按钮状态，设置当前按钮为选中状态

### **D.4 表单控件交互修复 (2024年12月更新)**
#### **修复内容**
- **问题描述**：`publish-transportation.html`页面中线路类型和服务范围选项点击无响应
- **解决方案**：为`label`元素添加点击事件监听器，确保点击时能正确触发内部`radio`按钮的`change`事件
- **技术实现**：使用`addEventListener`为每个`label`添加点击事件处理

## **附录E：出行板块实现更新记录**

### **D.1 出行卡片类型实现状态 (2024年12月更新)**

**已实现的5种出行类型卡片**：

1. **人找车卡片**
   - **标签样式**：蓝色渐变背景 (from-blue-500 to-blue-600)
   - **核心信息**：起点终点、出发时间、人数同行、途径信息、价格(每人)、联系方式
   - **特色功能**：途径站点显示、人数统计、发布时间展示

2. **车找人卡片**
   - **标签样式**：绿色渐变背景 (from-green-500 to-green-600)
   - **核心信息**：起点终点、出发时间、车辆信息、可载人数、价格、联系方式
   - **特色功能**：车辆类型展示、剩余座位数、路线固定性标识

3. **货找车卡片**
   - **标签样式**：橙色渐变背景 (from-orange-500 to-orange-600)
   - **核心信息**：起点终点、货物信息、重量体积、装卸要求、价格、联系方式
   - **特色功能**：货物类型分类、重量规格、特殊要求标注

4. **车找货卡片**
   - **标签样式**：紫色渐变背景 (from-purple-500 to-purple-600)
   - **核心信息**：服务路线、车辆类型、载重能力、服务范围、价格、联系方式
   - **特色功能**：车辆规格展示、载重限制、服务区域标识

5. **代驾服务卡片**
   - **标签样式**：青色渐变背景 (from-cyan-500 to-cyan-600)
   - **核心信息**：服务区域、驾龄经验、服务时间、价格标准、联系方式
   - **特色功能**：经验年限展示、服务时段、价格计费方式

### **D.2 界面设计特点**

- **统一视觉风格**：所有卡片采用相同的布局结构和间距规范
- **颜色区分系统**：不同类型使用不同的渐变色彩进行视觉区分
- **信息层次清晰**：标题、价格、时间、联系方式等信息按重要性分层展示
- **交互友好**：电话按钮、途径信息等关键操作元素突出显示
- **响应式设计**：适配移动端显示，确保在不同屏幕尺寸下的良好体验

### **D.3 出行卡片优化记录 (2024年12月更新)**

#### **优化记录1：信息精简优化**
- **优化时间**：2024年12月
- **优化内容**：
  - 删除"人找车"卡片中的"非固定线路"标签和"途径"信息
  - 删除"货找车"卡片中的"途径"信息
- **优化理由**：
  - 人找车通常是点对点直达，途径信息冗余
  - 货找车通常是直达配送，简化信息提升用户体验
- **效果**：界面更简洁明了，用户关注重点更突出

#### **优化记录2：认证与联系方式标准化**
- **优化时间**：2024年12月
- **优化内容**：
  - **认证标签统一**：为所有卡片添加"已认证"或"未认证"状态标签
  - **联系方式标准化**：将所有"微信"标签统一改为"电话"标签
  - **线路类型补充**：为"人找车"卡片重新添加"非固定线路"标签
  - **状态信息增强**：为"代驾服务"卡片添加"距离"和"在线"状态标签
  - **服务信息优化**：
    - 删除"24小时服务"标签，改为具体"服务时间：8:00-24:00"
    - 将"服务范围"统一规范为"城区内"或"全县城"
- **认证状态分配**：
  - 人找车：已认证
  - 车找人：已认证  
  - 货找车：未认证
  - 车找货：已认证
  - 代驾服务：已认证
- **在线状态逻辑**：代驾服务根据设定的服务时间(8:00-24:00)显示在线/离线状态
- **优化理由**：
  - 提升用户信任度和安全感
  - 统一联系方式，便于用户理解和使用
  - 增加实用的距离和状态信息
  - 明确服务时间和范围，避免误解
- **效果**：用户体验更加统一和专业，信息更加准确实用

---

**文档结束**

*本PRD文档是产品开发的核心指导文件，所有团队成员必须严格遵循文档要求，确保产品按照既定方向高质量交付。*