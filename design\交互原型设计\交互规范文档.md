# 交互规范文档

## 🎯 交互设计原则

### 1. 一致性原则
- **视觉一致性**: 相同功能使用相同的视觉元素
- **行为一致性**: 相同操作产生相同的结果
- **位置一致性**: 相同功能在相同位置出现

### 2. 反馈原则
- **即时反馈**: 用户操作后立即给出反馈
- **明确反馈**: 反馈信息清晰明确
- **适度反馈**: 反馈强度与操作重要性匹配

### 3. 容错原则
- **预防错误**: 通过设计减少用户犯错
- **错误恢复**: 提供简单的错误恢复方式
- **友好提示**: 错误信息友好且具有指导性

## 📱 基础交互规范

### 点击交互
```css
/* 标准点击反馈 */
.clickable {
  transition: all 0.2s ease;
  cursor: pointer;
}

.clickable:active {
  transform: scale(0.98);
  opacity: 0.8;
}

/* 按钮点击反馈 */
.button:active {
  transform: scale(0.96);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

/* 卡片点击反馈 */
.card:active {
  transform: scale(0.99);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}
```

### 长按交互
```css
/* 长按反馈 */
.long-pressable {
  position: relative;
}

.long-press-active {
  animation: longPressRipple 0.6s ease-out;
}

@keyframes longPressRipple {
  0% {
    box-shadow: 0 0 0 0 rgba(7, 193, 96, 0.4);
  }
  100% {
    box-shadow: 0 0 0 20rpx rgba(7, 193, 96, 0);
  }
}
```

### 滑动交互
```css
/* 滑动容器 */
.swipeable {
  overflow: hidden;
  position: relative;
}

/* 滑动指示器 */
.swipe-indicator {
  position: absolute;
  bottom: 20rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8rpx;
}

.indicator-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  transition: all 0.3s ease;
}

.indicator-dot.active {
  background: white;
  transform: scale(1.2);
}
```

## 🔄 状态交互规范

### 加载状态
```css
/* 页面加载 */
.page-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400rpx;
  gap: 20rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #EBEDF0;
  border-top-color: #07C160;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 24rpx;
  color: #8B9197;
}

/* 按钮加载 */
.button-loading {
  position: relative;
  color: transparent;
  pointer-events: none;
}

.button-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 24rpx;
  height: 24rpx;
  border: 2rpx solid currentColor;
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 骨架屏加载 */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
```

### 错误状态
```css
/* 错误页面 */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400rpx;
  padding: 60rpx 30rpx;
  text-align: center;
}

.error-icon {
  width: 120rpx;
  height: 120rpx;
  color: #FA5151;
  margin-bottom: 24rpx;
}

.error-title {
  font-size: 28rpx;
  color: #191F25;
  font-weight: 600;
  margin-bottom: 12rpx;
}

.error-description {
  font-size: 24rpx;
  color: #8B9197;
  line-height: 1.5;
  margin-bottom: 32rpx;
}

.error-retry {
  padding: 16rpx 32rpx;
  background: #07C160;
  color: white;
  border-radius: 8rpx;
  font-size: 26rpx;
  font-weight: 500;
}

/* 表单错误 */
.form-error {
  border-color: #FA5151;
  animation: errorShake 0.5s ease-in-out;
}

@keyframes errorShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-8rpx); }
  75% { transform: translateX(8rpx); }
}

.error-message {
  display: flex;
  align-items: center;
  gap: 6rpx;
  font-size: 22rpx;
  color: #FA5151;
  margin-top: 8rpx;
}

.error-message-icon {
  width: 16rpx;
  height: 16rpx;
}
```

### 空状态
```css
/* 空状态页面 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400rpx;
  padding: 60rpx 30rpx;
  text-align: center;
}

.empty-icon {
  width: 160rpx;
  height: 160rpx;
  color: #C5CAD0;
  margin-bottom: 24rpx;
}

.empty-title {
  font-size: 28rpx;
  color: #8B9197;
  font-weight: 500;
  margin-bottom: 12rpx;
}

.empty-description {
  font-size: 24rpx;
  color: #C5CAD0;
  line-height: 1.5;
  margin-bottom: 32rpx;
}

.empty-action {
  padding: 16rpx 32rpx;
  background: #07C160;
  color: white;
  border-radius: 8rpx;
  font-size: 26rpx;
  font-weight: 500;
}
```

## 🎨 动画交互规范

### 页面转场动画
```css
/* 页面进入 */
.page-enter {
  animation: pageEnter 0.3s ease-out;
}

@keyframes pageEnter {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 页面退出 */
.page-exit {
  animation: pageExit 0.3s ease-in;
}

@keyframes pageExit {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(-100%);
    opacity: 0;
  }
}

/* 模态框动画 */
.modal-enter {
  animation: modalEnter 0.3s ease-out;
}

@keyframes modalEnter {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.modal-exit {
  animation: modalExit 0.3s ease-in;
}

@keyframes modalExit {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(100%);
    opacity: 0;
  }
}
```

### 元素动画
```css
/* 淡入动画 */
.fade-in {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* 滑入动画 */
.slide-in-up {
  animation: slideInUp 0.4s ease-out;
}

@keyframes slideInUp {
  from {
    transform: translateY(40rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 缩放动画 */
.scale-in {
  animation: scaleIn 0.3s ease-out;
}

@keyframes scaleIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* 弹跳动画 */
.bounce-in {
  animation: bounceIn 0.6s ease-out;
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
    opacity: 1;
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
  }
}
```

## 📲 手势交互规范

### 滑动手势
```javascript
// 滑动配置
const swipeConfig = {
  // 最小滑动距离
  minDistance: 50,
  // 最大滑动时间
  maxTime: 300,
  // 滑动速度阈值
  velocityThreshold: 0.3,
  // 滑动角度容差
  angleThreshold: 30
}

// 滑动方向判断
function getSwipeDirection(startX, startY, endX, endY) {
  const deltaX = endX - startX
  const deltaY = endY - startY
  const angle = Math.atan2(deltaY, deltaX) * 180 / Math.PI
  
  if (Math.abs(angle) < 45) return 'right'
  if (Math.abs(angle) > 135) return 'left'
  if (angle > 45 && angle < 135) return 'down'
  if (angle < -45 && angle > -135) return 'up'
}
```

### 长按手势
```javascript
// 长按配置
const longPressConfig = {
  // 长按时间阈值
  duration: 500,
  // 移动容差
  moveTolerance: 10,
  // 反馈延迟
  feedbackDelay: 100
}

// 长按实现
function handleLongPress(element, callback) {
  let timer = null
  let startPos = null
  
  element.addEventListener('touchstart', (e) => {
    startPos = {
      x: e.touches[0].clientX,
      y: e.touches[0].clientY
    }
    
    timer = setTimeout(() => {
      callback()
      // 触发震动反馈
      wx.vibrateShort()
    }, longPressConfig.duration)
  })
  
  element.addEventListener('touchmove', (e) => {
    if (!startPos) return
    
    const currentPos = {
      x: e.touches[0].clientX,
      y: e.touches[0].clientY
    }
    
    const distance = Math.sqrt(
      Math.pow(currentPos.x - startPos.x, 2) +
      Math.pow(currentPos.y - startPos.y, 2)
    )
    
    if (distance > longPressConfig.moveTolerance) {
      clearTimeout(timer)
    }
  })
  
  element.addEventListener('touchend', () => {
    clearTimeout(timer)
    startPos = null
  })
}
```

## 🔔 反馈交互规范

### Toast 提示
```css
/* Toast 样式 */
.toast {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 20rpx 32rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  z-index: 9999;
  animation: toastShow 0.3s ease-out;
}

@keyframes toastShow {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

.toast-success {
  background: rgba(82, 196, 26, 0.9);
}

.toast-error {
  background: rgba(250, 81, 81, 0.9);
}

.toast-warning {
  background: rgba(255, 184, 0, 0.9);
}
```

### 震动反馈
```javascript
// 震动反馈配置
const vibrationConfig = {
  // 轻微震动 - 用于按钮点击
  light: {
    type: 'impact',
    style: 'light'
  },
  
  // 中等震动 - 用于重要操作
  medium: {
    type: 'impact',
    style: 'medium'
  },
  
  // 强烈震动 - 用于错误或警告
  heavy: {
    type: 'impact',
    style: 'heavy'
  },
  
  // 成功震动 - 用于操作成功
  success: {
    type: 'notification',
    style: 'success'
  },
  
  // 错误震动 - 用于操作失败
  error: {
    type: 'notification',
    style: 'error'
  }
}

// 震动反馈函数
function triggerVibration(type) {
  const config = vibrationConfig[type]
  if (!config) return
  
  if (config.type === 'impact') {
    wx.vibrateShort({
      type: config.style
    })
  } else if (config.type === 'notification') {
    wx.vibrateLong()
  }
}
```

## 🎯 可访问性规范

### 焦点管理
```css
/* 焦点样式 */
.focusable:focus {
  outline: 2rpx solid #07C160;
  outline-offset: 2rpx;
}

/* 跳过链接 */
.skip-link {
  position: absolute;
  top: -80rpx;
  left: 20rpx;
  background: #07C160;
  color: white;
  padding: 16rpx 24rpx;
  border-radius: 8rpx;
  text-decoration: none;
  z-index: 10000;
}

.skip-link:focus {
  top: 20rpx;
}
```

### 语义化标记
```html
<!-- 正确的语义化结构 -->
<view role="main" aria-label="主要内容">
  <view role="navigation" aria-label="主导航">
    <button aria-label="商铺板块" aria-pressed="false">
      商铺
    </button>
  </view>
  
  <view role="list" aria-label="信息列表">
    <view role="listitem" tabindex="0" aria-label="招聘信息">
      <!-- 列表项内容 -->
    </view>
  </view>
</view>
```

### 文本替代
```html
<!-- 图片替代文本 -->
<image src="shop.jpg" alt="张三水果店门面照片" />

<!-- 图标替代文本 -->
<view class="icon-phone" aria-label="电话联系" role="button">
  <image src="phone-icon.svg" alt="" />
</view>

<!-- 装饰性图片 -->
<image src="decoration.jpg" alt="" role="presentation" />
```

---

**交互规范要点**:
1. **一致性** - 相同功能保持相同的交互方式
2. **可预测性** - 用户能够预期交互的结果
3. **即时反馈** - 每个操作都有明确的反馈
4. **容错性** - 提供清晰的错误信息和恢复路径
5. **可访问性** - 考虑不同用户的使用需求
