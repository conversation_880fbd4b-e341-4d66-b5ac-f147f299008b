# 本地助手小程序 - 设计指导文档

## 🎨 设计概述

本文档将指导您使用Figma创建"本地助手"小程序的高保真UI/UX原型，严格遵循PRD要求和微信小程序设计规范。

## 📐 设计规范

### 1. 画布设置
- **设备尺寸**: iPhone 14 (390 × 844 px)
- **设计尺寸**: 750 × 1624 rpx (微信小程序标准)
- **安全区域**: 顶部44px，底部34px
- **状态栏**: 高度44px，背景色#07C160

### 2. 色彩系统

#### 主色调
```
主色 (Primary): #07C160    // 微信绿
辅助色 (Secondary): #10AEFF  // 蓝色
强调色 (Accent): #FF6B35     // 橙色
警告色 (Warning): #FFB800    // 黄色
错误色 (Error): #FA5151      // 红色
```

#### 文本色彩
```
主要文本: #191F25
次要文本: #8B9197
占位文本: #C5CAD0
```

#### 背景色彩
```
主背景: #FFFFFF
次背景: #F8F9FA
边框色: #EBEDF0
```

#### 板块专用色彩
```
商铺板块: #667eea → #764ba2 (蓝紫渐变)
信息板块: #f093fb → #f5576c (粉红渐变)
出行板块: #4facfe → #00f2fe (蓝青渐变)
跑腿板块: #43e97b → #38f9d7 (绿青渐变)
```

### 3. 字体规范

#### 字体家族
- **iOS**: -apple-system, SF Pro Display
- **Android**: PingFang SC, Microsoft YaHei
- **Figma**: Inter (作为设计字体)

#### 字号规范
```
特大标题: 36rpx (18px) - 页面主标题
大标题: 32rpx (16px) - 区块标题
中标题: 28rpx (14px) - 卡片标题
正文: 26rpx (13px) - 正文内容
小字: 24rpx (12px) - 辅助信息
极小字: 20rpx (10px) - 标签文字
```

### 4. 间距系统

#### 基础间距单位: 4rpx (2px)
```
xs: 8rpx (4px)
sm: 12rpx (6px)
md: 16rpx (8px)
lg: 20rpx (10px)
xl: 24rpx (12px)
2xl: 32rpx (16px)
3xl: 48rpx (24px)
4xl: 64rpx (32px)
```

#### 页面间距
```
页面边距: 30rpx (15px)
卡片间距: 20rpx (10px)
内容间距: 16rpx (8px)
```

### 5. 圆角规范
```
小圆角: 8rpx (4px) - 标签
中圆角: 12rpx (6px) - 按钮、输入框
大圆角: 20rpx (10px) - 卡片
圆形: 50% - 头像、图标背景
```

### 6. 阴影规范
```
轻阴影: 0 2rpx 12rpx rgba(0, 0, 0, 0.1)
中阴影: 0 4rpx 20rpx rgba(0, 0, 0, 0.15)
重阴影: 0 8rpx 32rpx rgba(0, 0, 0, 0.2)
```

## 📱 页面设计要求

### 1. 首页 (index)
#### 必需元素
- [x] 顶部搜索栏 + 扫码按钮
- [x] 扫码呼叫服务卡片
- [x] 四大板块导航网格
- [x] 快速发布功能区
- [x] 最新信息流列表
- [x] 底部Tab导航

#### 设计重点
- 扫码服务卡片使用渐变背景突出显示
- 四大板块使用不同色彩区分
- 信息流卡片设计简洁明了

### 2. 商铺板块 (shops)
#### 必需元素
- [ ] 顶部分类筛选栏
- [ ] 搜索和筛选功能
- [ ] 商家卡片列表
- [ ] 地图视图切换
- [ ] 发布商铺入口

#### 商家卡片设计
- 商家头像 + 名称
- 主营业务描述
- 营业时间
- 距离显示
- 认证标识
- 联系按钮

### 3. 信息板块 (information)
#### 必需元素
- [ ] 分类标签栏 (全部、招聘、房屋、二手、其他)
- [ ] 信息卡片列表
- [ ] 搜索功能
- [ ] 发布信息入口

#### 信息卡片设计
- 信息标题
- 内容摘要
- 发布时间
- 位置信息
- 联系方式

### 4. 出行板块 (transportation)
#### 必需元素
- [ ] 出行类型筛选 (人找车、车找人、货找车、车找货、代驾)
- [ ] 出行信息卡片
- [ ] 路线显示
- [ ] 发布出行信息入口

#### 出行卡片设计
- 出行类型标签
- 起点终点
- 出发时间
- 价格信息
- 联系方式

### 5. 跑腿板块 (errands)
#### 必需元素
- [ ] 服务类型筛选
- [ ] 跑腿任务卡片
- [ ] 紧急程度标识
- [ ] 发布跑腿任务入口

#### 跑腿卡片设计
- 任务类型
- 任务描述
- 时间要求
- 报酬信息
- 联系方式

### 6. 发布页面 (publish)
#### 必需元素
- [ ] 发布类型选择
- [ ] 表单输入界面
- [ ] 图片上传功能
- [ ] 位置选择
- [ ] 发布按钮

### 7. 详情页面 (detail)
#### 必需元素
- [ ] 详细信息展示
- [ ] 图片轮播
- [ ] 联系方式
- [ ] 地图位置
- [ ] 分享功能

### 8. 个人中心 (profile)
#### 必需元素
- [ ] 用户信息
- [ ] 我的发布
- [ ] 我的收藏
- [ ] 设置选项

## 🔧 Figma设计流程

### 第一步：创建项目
1. 访问 https://www.figma.com/
2. 注册/登录账号
3. 创建新项目 "本地助手小程序"
4. 设置团队协作权限

### 第二步：建立设计系统
1. 创建色彩样式库
2. 建立文字样式库
3. 设计基础组件库
4. 创建图标库

### 第三步：页面设计
1. 按照页面清单逐一设计
2. 保持设计一致性
3. 添加交互原型
4. 进行设计评审

### 第四步：交付准备
1. 整理设计文件
2. 导出切图资源
3. 编写设计说明
4. 准备演示原型

## 📋 设计检查清单

### 视觉设计
- [ ] 色彩使用符合规范
- [ ] 字体大小层次清晰
- [ ] 间距使用一致
- [ ] 圆角规范统一
- [ ] 阴影效果适当

### 交互设计
- [ ] 点击区域足够大
- [ ] 反馈状态明确
- [ ] 导航逻辑清晰
- [ ] 错误处理友好
- [ ] 加载状态完整

### 微信规范
- [ ] 符合小程序设计指南
- [ ] Tab栏设计规范
- [ ] 导航栏设计规范
- [ ] 安全区域适配
- [ ] 组件使用正确

## 🎯 设计目标

### 用户体验目标
- **简单易用** - 3步内完成核心任务
- **信息清晰** - 重要信息突出显示
- **操作便捷** - 减少用户认知负担
- **视觉舒适** - 现代简约的设计风格

### 技术实现目标
- **开发友好** - 设计可完全实现
- **性能优化** - 考虑加载速度
- **兼容性好** - 适配不同设备
- **维护性强** - 组件化设计

---

**下一步**: 开始在Figma中创建设计项目，我将为您提供详细的设计指导和反馈。
