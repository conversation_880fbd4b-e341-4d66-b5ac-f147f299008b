# 详情页面设计规范

## 📱 页面概述

详情页面展示各类信息的完整内容，包括商铺详情、信息详情、出行详情、跑腿详情等，提供完整的信息展示和联系功能。

## 🎨 整体布局

### 页面结构
```
详情页面 (750rpx × 1334rpx)
├── 导航栏 (88rpx)
├── 图片轮播区域 (400rpx)
├── 基本信息区域 (可变高度)
├── 详细内容区域 (可滚动)
├── 联系信息区域 (120rpx)
└── 底部操作栏 (120rpx)
```

## 🧭 导航栏设计

```css
.detail-nav-bar {
  height: 88rpx;
  background: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  border-bottom: 1rpx solid #EBEDF0;
}

.nav-back {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #191F25;
}

.nav-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #191F25;
}

.nav-actions {
  display: flex;
  gap: 16rpx;
}

.nav-action {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #8B9197;
}
```

## 📷 图片轮播区域

### 轮播组件
```css
.image-carousel {
  height: 400rpx;
  position: relative;
  background: #F0F0F0;
}

.carousel-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.carousel-wrapper {
  display: flex;
  height: 100%;
  transition: transform 0.3s ease;
}

.carousel-item {
  width: 100%;
  height: 100%;
  flex-shrink: 0;
}

.carousel-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.carousel-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #F8F9FA;
  gap: 16rpx;
}

.placeholder-icon {
  width: 80rpx;
  height: 80rpx;
  color: #C5CAD0;
}

.placeholder-text {
  font-size: 24rpx;
  color: #C5CAD0;
}

.carousel-indicators {
  position: absolute;
  bottom: 20rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8rpx;
}

.indicator {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  transition: background-color 0.2s ease;
}

.indicator-active {
  background: white;
}

.carousel-counter {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  padding: 8rpx 16rpx;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  border-radius: 20rpx;
  font-size: 22rpx;
}
```

## 📋 基本信息区域

### 信息头部
```css
.detail-header {
  background: white;
  padding: 30rpx;
  border-bottom: 1rpx solid #EBEDF0;
}

.detail-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #191F25;
  line-height: 1.3;
  margin-bottom: 16rpx;
}

.detail-meta {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 20rpx;
}

.meta-tag {
  padding: 6rpx 12rpx;
  border-radius: 6rpx;
  font-size: 20rpx;
  font-weight: 500;
  color: white;
}

.meta-tag-shops {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.meta-tag-info {
  background: linear-gradient(135deg, #f093fb, #f5576c);
}

.meta-tag-transport {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.meta-tag-errands {
  background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.meta-time {
  font-size: 22rpx;
  color: #8B9197;
}

.meta-location {
  display: flex;
  align-items: center;
  gap: 6rpx;
  font-size: 22rpx;
  color: #8B9197;
}

.location-icon {
  width: 16rpx;
  height: 16rpx;
}

.detail-price {
  display: flex;
  align-items: baseline;
  gap: 8rpx;
  margin-bottom: 20rpx;
}

.price-amount {
  font-size: 48rpx;
  font-weight: 700;
  color: #FA5151;
}

.price-unit {
  font-size: 24rpx;
  color: #8B9197;
}

.price-type {
  padding: 4rpx 8rpx;
  background: rgba(250, 81, 81, 0.1);
  color: #FA5151;
  border-radius: 4rpx;
  font-size: 20rpx;
}

.detail-stats {
  display: flex;
  gap: 24rpx;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6rpx;
  font-size: 22rpx;
  color: #8B9197;
}

.stat-icon {
  width: 16rpx;
  height: 16rpx;
}
```

## 📝 详细内容区域

### 内容分区
```css
.detail-content {
  background: white;
}

.content-section {
  padding: 30rpx;
  border-bottom: 1rpx solid #EBEDF0;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #191F25;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.section-icon {
  width: 24rpx;
  height: 24rpx;
  color: #07C160;
}

.section-content {
  font-size: 26rpx;
  color: #191F25;
  line-height: 1.6;
}

.content-text {
  margin-bottom: 16rpx;
}

.content-list {
  margin-bottom: 16rpx;
}

.list-item {
  display: flex;
  align-items: flex-start;
  gap: 8rpx;
  margin-bottom: 8rpx;
}

.list-bullet {
  width: 8rpx;
  height: 8rpx;
  background: #07C160;
  border-radius: 50%;
  margin-top: 12rpx;
  flex-shrink: 0;
}

.list-text {
  flex: 1;
}
```

### 商铺详情特殊内容
```css
.shop-info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin-bottom: 24rpx;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.info-label {
  font-size: 22rpx;
  color: #8B9197;
}

.info-value {
  font-size: 26rpx;
  color: #191F25;
  font-weight: 500;
}

.business-hours {
  background: rgba(102, 126, 234, 0.05);
  padding: 20rpx;
  border-radius: 12rpx;
  margin-bottom: 24rpx;
}

.hours-title {
  font-size: 24rpx;
  color: #667eea;
  font-weight: 600;
  margin-bottom: 12rpx;
}

.hours-list {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.hours-item {
  display: flex;
  justify-content: space-between;
  font-size: 22rpx;
}

.hours-day {
  color: #8B9197;
}

.hours-time {
  color: #191F25;
  font-weight: 500;
}

.hours-closed {
  color: #FA5151;
}

.shop-features {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-bottom: 24rpx;
}

.feature-tag {
  padding: 8rpx 12rpx;
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  border-radius: 6rpx;
  font-size: 22rpx;
}
```

### 出行详情特殊内容
```css
.route-section {
  background: rgba(79, 172, 254, 0.05);
  padding: 20rpx;
  border-radius: 12rpx;
  margin-bottom: 24rpx;
}

.route-title {
  font-size: 24rpx;
  color: #4facfe;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.route-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.route-item:last-child {
  margin-bottom: 0;
}

.route-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  flex-shrink: 0;
}

.route-start {
  background: #52C41A;
}

.route-end {
  background: #FA5151;
}

.route-info {
  flex: 1;
}

.route-address {
  font-size: 26rpx;
  color: #191F25;
  font-weight: 500;
  margin-bottom: 4rpx;
}

.route-time {
  font-size: 22rpx;
  color: #8B9197;
}

.route-line {
  width: 2rpx;
  height: 24rpx;
  background: #EBEDF0;
  margin-left: 7rpx;
  margin-bottom: 8rpx;
}

.vehicle-info {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.vehicle-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 16rpx;
  background: #F8F9FA;
  border-radius: 8rpx;
}

.vehicle-icon {
  width: 20rpx;
  height: 20rpx;
  color: #4facfe;
}

.vehicle-text {
  font-size: 22rpx;
  color: #191F25;
}
```

## 👤 联系信息区域

### 联系方式展示
```css
.contact-section {
  background: white;
  padding: 30rpx;
  border-bottom: 1rpx solid #EBEDF0;
}

.contact-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 20rpx;
}

.contact-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #F0F0F0;
}

.contact-info {
  flex: 1;
}

.contact-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #191F25;
  margin-bottom: 4rpx;
}

.contact-meta {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.contact-rating {
  display: flex;
  align-items: center;
  gap: 4rpx;
}

.rating-stars {
  display: flex;
  gap: 2rpx;
}

.star {
  width: 16rpx;
  height: 16rpx;
  color: #FFB800;
}

.rating-score {
  font-size: 22rpx;
  color: #8B9197;
}

.contact-badge {
  padding: 2rpx 6rpx;
  background: #52C41A;
  color: white;
  border-radius: 4rpx;
  font-size: 18rpx;
}

.contact-methods {
  display: flex;
  gap: 12rpx;
}

.contact-method {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx;
  background: #F8F9FA;
  border-radius: 12rpx;
}

.method-icon {
  width: 32rpx;
  height: 32rpx;
  color: #07C160;
}

.method-text {
  font-size: 22rpx;
  color: #191F25;
  font-weight: 500;
}
```

## 🗺️ 地图位置

### 地图组件
```css
.map-section {
  background: white;
  padding: 30rpx;
  border-bottom: 1rpx solid #EBEDF0;
}

.map-container {
  height: 300rpx;
  border-radius: 12rpx;
  overflow: hidden;
  margin-bottom: 16rpx;
}

.map-placeholder {
  width: 100%;
  height: 100%;
  background: #F8F9FA;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
}

.map-icon {
  width: 60rpx;
  height: 60rpx;
  color: #C5CAD0;
}

.map-text {
  font-size: 24rpx;
  color: #C5CAD0;
}

.map-address {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: #191F25;
}

.address-icon {
  width: 20rpx;
  height: 20rpx;
  color: #07C160;
}

.map-actions {
  display: flex;
  gap: 12rpx;
  margin-top: 16rpx;
}

.map-action {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 12rpx;
  background: #F8F9FA;
  border-radius: 8rpx;
  font-size: 22rpx;
  color: #07C160;
}

.action-icon {
  width: 20rpx;
  height: 20rpx;
}
```

## 🎯 底部操作栏

### 操作按钮
```css
.detail-bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #EBEDF0;
  display: flex;
  gap: 16rpx;
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.action-icon-btn {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4rpx;
  background: #F8F9FA;
  border-radius: 12rpx;
  color: #8B9197;
}

.action-icon {
  width: 28rpx;
  height: 28rpx;
}

.action-text {
  font-size: 18rpx;
}

.action-primary {
  flex: 1;
  height: 80rpx;
  background: #07C160;
  border-radius: 12rpx;
  color: white;
  font-size: 28rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.3);
}

.action-primary:active {
  background: #06AD56;
}

.action-call {
  background: #10AEFF;
  box-shadow: 0 4rpx 12rpx rgba(16, 174, 255, 0.3);
}

.action-call:active {
  background: #0E9AE6;
}
```

## 📱 交互反馈

### 加载状态
```css
.detail-loading {
  padding: 60rpx 30rpx;
  text-align: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #EBEDF0;
  border-top-color: #07C160;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20rpx;
}

.loading-text {
  font-size: 24rpx;
  color: #8B9197;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
```

### 错误状态
```css
.detail-error {
  padding: 60rpx 30rpx;
  text-align: center;
}

.error-icon {
  width: 80rpx;
  height: 80rpx;
  color: #FA5151;
  margin: 0 auto 20rpx;
}

.error-title {
  font-size: 28rpx;
  color: #191F25;
  font-weight: 600;
  margin-bottom: 12rpx;
}

.error-desc {
  font-size: 24rpx;
  color: #8B9197;
  line-height: 1.5;
  margin-bottom: 24rpx;
}

.error-retry {
  padding: 12rpx 24rpx;
  background: #07C160;
  color: white;
  border-radius: 8rpx;
  font-size: 24rpx;
  font-weight: 500;
}
```

---

**设计要点**:
1. 图片轮播突出展示，支持多图浏览
2. 信息层次清晰，重要信息突出显示
3. 联系方式便捷，支持多种联系方式
4. 地图位置直观，便于用户导航
