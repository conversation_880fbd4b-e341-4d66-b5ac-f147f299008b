# 发布页面设计规范

## 📱 页面概述

发布页面是用户发布各类信息的统一入口，支持商铺、信息、出行、跑腿四大类型的信息发布。

## 🎨 整体布局

### 页面结构
```
发布页面 (750rpx × 1334rpx)
├── 导航栏 (88rpx)
├── 发布类型选择 (120rpx)
├── 表单内容区域 (可滚动)
├── 底部操作栏 (120rpx)
└── Tab栏 (98rpx)
```

## 🧭 导航栏设计

```css
.nav-bar {
  height: 88rpx;
  background: #07C160;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  color: white;
}

.nav-back {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-title {
  font-size: 32rpx;
  font-weight: 600;
}

.nav-help {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
```

## 🏷️ 发布类型选择

### 类型选择器
```css
.publish-type-selector {
  background: white;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #EBEDF0;
}

.type-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16rpx;
}

.type-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx 12rpx;
  border-radius: 12rpx;
  background: #F8F9FA;
  border: 2rpx solid transparent;
  transition: all 0.2s ease;
}

.type-item-active {
  border-color: #07C160;
  background: rgba(7, 193, 96, 0.05);
}

.type-shops.type-item-active {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.05);
}

.type-info.type-item-active {
  border-color: #f093fb;
  background: rgba(240, 147, 251, 0.05);
}

.type-transport.type-item-active {
  border-color: #4facfe;
  background: rgba(79, 172, 254, 0.05);
}

.type-errands.type-item-active {
  border-color: #43e97b;
  background: rgba(67, 233, 123, 0.05);
}

.type-icon {
  width: 32rpx;
  height: 32rpx;
  margin-bottom: 8rpx;
  color: #8B9197;
}

.type-item-active .type-icon {
  color: #07C160;
}

.type-text {
  font-size: 22rpx;
  font-weight: 500;
  color: #8B9197;
}

.type-item-active .type-text {
  color: #07C160;
}
```

## 📝 表单设计

### 基础表单容器
```css
.form-container {
  background: white;
  padding: 30rpx;
}

.form-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #191F25;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.required-mark {
  color: #FA5151;
  font-size: 24rpx;
}

.section-desc {
  font-size: 22rpx;
  color: #8B9197;
  margin-bottom: 16rpx;
  line-height: 1.5;
}
```

### 输入框组件
```css
.form-input {
  width: 100%;
  height: 80rpx;
  padding: 0 24rpx;
  border: 2rpx solid #EBEDF0;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: white;
  color: #191F25;
  transition: border-color 0.2s ease;
}

.form-input:focus {
  border-color: #07C160;
  box-shadow: 0 0 0 4rpx rgba(7, 193, 96, 0.1);
}

.form-input::placeholder {
  color: #C5CAD0;
}

.form-textarea {
  width: 100%;
  min-height: 160rpx;
  padding: 20rpx 24rpx;
  border: 2rpx solid #EBEDF0;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: white;
  color: #191F25;
  resize: none;
  line-height: 1.5;
}

.form-textarea:focus {
  border-color: #07C160;
  box-shadow: 0 0 0 4rpx rgba(7, 193, 96, 0.1);
}

.char-count {
  text-align: right;
  font-size: 22rpx;
  color: #8B9197;
  margin-top: 8rpx;
}

.char-count-warning {
  color: #FFB800;
}

.char-count-error {
  color: #FA5151;
}
```

### 选择器组件
```css
.form-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
  padding: 0 24rpx;
  border: 2rpx solid #EBEDF0;
  border-radius: 12rpx;
  background: white;
  color: #191F25;
  font-size: 28rpx;
}

.selector-placeholder {
  color: #C5CAD0;
}

.selector-arrow {
  width: 24rpx;
  height: 24rpx;
  color: #8B9197;
  transition: transform 0.2s ease;
}

.selector-open .selector-arrow {
  transform: rotate(180deg);
}

.selector-options {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 2rpx solid #EBEDF0;
  border-top: none;
  border-radius: 0 0 12rpx 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.selector-option {
  padding: 20rpx 24rpx;
  font-size: 28rpx;
  color: #191F25;
  border-bottom: 1rpx solid #EBEDF0;
}

.selector-option:last-child {
  border-bottom: none;
}

.selector-option:active {
  background: #F8F9FA;
}

.selector-option-selected {
  color: #07C160;
  font-weight: 500;
}
```

## 📷 图片上传组件

### 图片上传区域
```css
.image-upload-section {
  margin-bottom: 40rpx;
}

.upload-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}

.upload-item {
  aspect-ratio: 1;
  border-radius: 12rpx;
  overflow: hidden;
  position: relative;
}

.upload-placeholder {
  width: 100%;
  height: 100%;
  background: #F8F9FA;
  border: 2rpx dashed #EBEDF0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  transition: all 0.2s ease;
}

.upload-placeholder:active {
  background: #F0F0F0;
  border-color: #07C160;
}

.upload-icon {
  width: 40rpx;
  height: 40rpx;
  color: #C5CAD0;
}

.upload-text {
  font-size: 22rpx;
  color: #C5CAD0;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-actions {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  display: flex;
  gap: 8rpx;
}

.image-action {
  width: 32rpx;
  height: 32rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.upload-tips {
  font-size: 22rpx;
  color: #8B9197;
  margin-top: 12rpx;
  line-height: 1.5;
}
```

## 📍 位置选择组件

### 位置选择器
```css
.location-section {
  margin-bottom: 40rpx;
}

.location-selector {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx 24rpx;
  border: 2rpx solid #EBEDF0;
  border-radius: 12rpx;
  background: white;
}

.location-icon {
  width: 32rpx;
  height: 32rpx;
  color: #07C160;
  flex-shrink: 0;
}

.location-content {
  flex: 1;
}

.location-address {
  font-size: 28rpx;
  color: #191F25;
  font-weight: 500;
  margin-bottom: 4rpx;
}

.location-detail {
  font-size: 22rpx;
  color: #8B9197;
}

.location-placeholder {
  font-size: 28rpx;
  color: #C5CAD0;
}

.location-arrow {
  width: 24rpx;
  height: 24rpx;
  color: #8B9197;
}

.current-location-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background: rgba(7, 193, 96, 0.1);
  color: #07C160;
  border-radius: 8rpx;
  font-size: 24rpx;
  font-weight: 500;
  margin-top: 16rpx;
}

.current-location-icon {
  width: 20rpx;
  height: 20rpx;
}
```

## 💰 价格设置组件

### 价格输入
```css
.price-section {
  margin-bottom: 40rpx;
}

.price-input-group {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.price-input {
  flex: 1;
  height: 80rpx;
  padding: 0 24rpx;
  border: 2rpx solid #EBEDF0;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 600;
  text-align: center;
  color: #07C160;
}

.price-unit {
  font-size: 28rpx;
  color: #8B9197;
  font-weight: 500;
}

.price-type-selector {
  display: flex;
  gap: 12rpx;
  margin-top: 16rpx;
}

.price-type {
  flex: 1;
  padding: 16rpx;
  border: 2rpx solid #EBEDF0;
  border-radius: 8rpx;
  text-align: center;
  font-size: 24rpx;
  color: #8B9197;
  background: white;
}

.price-type-active {
  border-color: #07C160;
  background: rgba(7, 193, 96, 0.05);
  color: #07C160;
  font-weight: 500;
}

.price-tips {
  background: rgba(7, 193, 96, 0.05);
  padding: 16rpx;
  border-radius: 8rpx;
  margin-top: 16rpx;
}

.price-tip {
  font-size: 22rpx;
  color: #07C160;
  line-height: 1.5;
}
```

## 📞 联系方式组件

### 联系信息
```css
.contact-section {
  margin-bottom: 40rpx;
}

.contact-methods {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.contact-method {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx 24rpx;
  border: 2rpx solid #EBEDF0;
  border-radius: 12rpx;
  background: white;
}

.contact-method-active {
  border-color: #07C160;
  background: rgba(7, 193, 96, 0.05);
}

.contact-checkbox {
  width: 24rpx;
  height: 24rpx;
  border: 2rpx solid #EBEDF0;
  border-radius: 4rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.contact-checkbox-checked {
  border-color: #07C160;
  background: #07C160;
}

.contact-check-icon {
  width: 16rpx;
  height: 16rpx;
  color: white;
}

.contact-icon {
  width: 32rpx;
  height: 32rpx;
  color: #07C160;
}

.contact-info {
  flex: 1;
}

.contact-type {
  font-size: 26rpx;
  color: #191F25;
  font-weight: 500;
  margin-bottom: 4rpx;
}

.contact-value {
  font-size: 22rpx;
  color: #8B9197;
}

.contact-privacy {
  background: rgba(255, 182, 0, 0.1);
  padding: 16rpx;
  border-radius: 8rpx;
  margin-top: 16rpx;
}

.privacy-text {
  font-size: 22rpx;
  color: #FFB800;
  line-height: 1.5;
}
```

## 🔒 隐私设置

### 隐私选项
```css
.privacy-section {
  margin-bottom: 40rpx;
}

.privacy-options {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.privacy-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 24rpx;
  background: #F8F9FA;
  border-radius: 12rpx;
}

.privacy-info {
  flex: 1;
}

.privacy-title {
  font-size: 26rpx;
  color: #191F25;
  font-weight: 500;
  margin-bottom: 4rpx;
}

.privacy-desc {
  font-size: 22rpx;
  color: #8B9197;
  line-height: 1.4;
}

.privacy-switch {
  width: 80rpx;
  height: 40rpx;
  background: #EBEDF0;
  border-radius: 20rpx;
  position: relative;
  transition: background-color 0.2s ease;
}

.privacy-switch-active {
  background: #07C160;
}

.privacy-switch-handle {
  width: 32rpx;
  height: 32rpx;
  background: white;
  border-radius: 50%;
  position: absolute;
  top: 4rpx;
  left: 4rpx;
  transition: transform 0.2s ease;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.privacy-switch-active .privacy-switch-handle {
  transform: translateX(40rpx);
}
```

## 🎯 底部操作栏

### 操作按钮
```css
.bottom-actions {
  position: fixed;
  bottom: 98rpx;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #EBEDF0;
  display: flex;
  gap: 16rpx;
}

.action-secondary {
  flex: 1;
  height: 80rpx;
  border: 2rpx solid #EBEDF0;
  border-radius: 12rpx;
  background: white;
  color: #8B9197;
  font-size: 28rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-primary {
  flex: 2;
  height: 80rpx;
  background: #07C160;
  border-radius: 12rpx;
  color: white;
  font-size: 28rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.3);
}

.action-primary:active {
  background: #06AD56;
}

.action-disabled {
  background: #EBEDF0;
  color: #C5CAD0;
  box-shadow: none;
}
```

## 📱 交互反馈

### 表单验证
```css
.form-error {
  border-color: #FA5151;
  box-shadow: 0 0 0 4rpx rgba(250, 81, 81, 0.1);
}

.error-message {
  font-size: 22rpx;
  color: #FA5151;
  margin-top: 8rpx;
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.error-icon {
  width: 16rpx;
  height: 16rpx;
}

.form-success {
  border-color: #52C41A;
  box-shadow: 0 0 0 4rpx rgba(82, 196, 26, 0.1);
}

.success-message {
  font-size: 22rpx;
  color: #52C41A;
  margin-top: 8rpx;
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.success-icon {
  width: 16rpx;
  height: 16rpx;
}
```

### 加载状态
```css
.form-loading {
  position: relative;
  pointer-events: none;
}

.form-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #EBEDF0;
  border-top-color: #07C160;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
```

---

**设计要点**:
1. 表单设计简洁明了，减少用户认知负担
2. 实时验证和反馈，提升用户体验
3. 图片上传和位置选择功能完善
4. 隐私设置透明，保护用户信息安全
