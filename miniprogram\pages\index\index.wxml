<!--本地助手小程序 - 首页-->
<view class="page-container">
  <!-- 顶部搜索栏 -->
  <view class="search-header">
    <view class="search-bar" bindtap="goToSearch">
      <icon class="search-icon" type="search" size="16"></icon>
      <text class="search-placeholder">搜索商家、服务、信息...</text>
    </view>
    <view class="scan-btn" bindtap="scanCode">
      <icon type="camera" size="20"></icon>
    </view>
  </view>

  <!-- 扫码呼叫服务 -->
  <view class="scan-service-section">
    <view class="scan-service-card" bindtap="scanCode">
      <view class="scan-icon">
        <icon type="camera" size="24"></icon>
      </view>
      <view class="scan-content">
        <text class="scan-title">扫码呼叫服务</text>
        <text class="scan-desc">扫描商家二维码，快速呼叫服务</text>
      </view>
      <view class="scan-arrow">
        <icon type="arrow" size="16"></icon>
      </view>
    </view>
  </view>

  <!-- 四大板块导航 -->
  <view class="nav-section">
    <view class="nav-title">服务分类</view>
    <view class="nav-grid">
      <view class="nav-item shops-nav" bindtap="goToShops">
        <view class="nav-icon">
          <image src="/images/nav/shops.png" mode="aspectFit"></image>
        </view>
        <text class="nav-text">商铺</text>
        <text class="nav-desc">本地商家服务</text>
      </view>
      
      <view class="nav-item info-nav" bindtap="goToInformation">
        <view class="nav-icon">
          <image src="/images/nav/information.png" mode="aspectFit"></image>
        </view>
        <text class="nav-text">信息</text>
        <text class="nav-desc">生活信息发布</text>
      </view>
      
      <view class="nav-item transport-nav" bindtap="goToTransportation">
        <view class="nav-icon">
          <image src="/images/nav/transportation.png" mode="aspectFit"></image>
        </view>
        <text class="nav-text">出行</text>
        <text class="nav-desc">出行信息对接</text>
      </view>
      
      <view class="nav-item errand-nav" bindtap="goToErrands">
        <view class="nav-icon">
          <image src="/images/nav/errands.png" mode="aspectFit"></image>
        </view>
        <text class="nav-text">跑腿</text>
        <text class="nav-desc">跑腿服务信息</text>
      </view>
    </view>
  </view>

  <!-- 快速发布 -->
  <view class="quick-publish-section">
    <view class="section-header">
      <text class="section-title">快速发布</text>
      <text class="section-more" bindtap="goToPublish">更多 ></text>
    </view>
    <view class="publish-grid">
      <view class="publish-item" bindtap="publishShop">
        <icon type="shop" size="20"></icon>
        <text>发布商铺</text>
      </view>
      <view class="publish-item" bindtap="publishInfo">
        <icon type="info" size="20"></icon>
        <text>发布信息</text>
      </view>
      <view class="publish-item" bindtap="publishTransport">
        <icon type="transport" size="20"></icon>
        <text>发布出行</text>
      </view>
      <view class="publish-item" bindtap="publishErrand">
        <icon type="errand" size="20"></icon>
        <text>发布跑腿</text>
      </view>
    </view>
  </view>

  <!-- 最新信息流 -->
  <view class="feed-section">
    <view class="section-header">
      <text class="section-title">最新发布</text>
      <text class="section-more">查看更多 ></text>
    </view>
    
    <view class="feed-list">
      <view class="feed-item" wx:for="{{feedList}}" wx:key="id" bindtap="goToDetail" data-item="{{item}}">
        <view class="feed-header">
          <view class="feed-type {{item.type}}">{{item.typeName}}</view>
          <view class="feed-time">{{item.publishTime}}</view>
        </view>
        <view class="feed-title">{{item.title}}</view>
        <view class="feed-content">{{item.content}}</view>
        <view class="feed-footer">
          <view class="feed-location">
            <icon type="location" size="12"></icon>
            <text>{{item.location}}</text>
          </view>
          <view class="feed-contact">
            <icon type="phone" size="12"></icon>
            <text>联系</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area-bottom"></view>
</view>
