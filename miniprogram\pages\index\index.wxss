/* 本地助手小程序 - 首页样式 */

/* 搜索头部 */
.search-header {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: var(--primary-color);
  gap: 20rpx;
}

.search-bar {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50rpx;
  padding: 16rpx 24rpx;
  gap: 12rpx;
}

.search-icon {
  color: var(--text-placeholder);
}

.search-placeholder {
  color: var(--text-placeholder);
  font-size: 28rpx;
}

.scan-btn {
  width: 80rpx;
  height: 80rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.scan-btn icon {
  color: white;
}

/* 扫码服务区域 */
.scan-service-section {
  padding: 30rpx;
}

.scan-service-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: var(--border-radius-large);
  padding: 30rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
  box-shadow: var(--shadow-medium);
}

.scan-icon {
  width: 80rpx;
  height: 80rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.scan-icon icon {
  color: white;
}

.scan-content {
  flex: 1;
  color: white;
}

.scan-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.scan-desc {
  display: block;
  font-size: 24rpx;
  opacity: 0.8;
}

.scan-arrow icon {
  color: white;
}

/* 导航区域 */
.nav-section {
  padding: 0 30rpx 30rpx;
}

.nav-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 30rpx;
}

.nav-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.nav-item {
  background-color: var(--bg-primary);
  border-radius: var(--border-radius-large);
  padding: 40rpx 30rpx;
  text-align: center;
  box-shadow: var(--shadow-light);
  transition: transform 0.2s ease;
}

.nav-item:active {
  transform: scale(0.98);
}

.nav-icon {
  width: 80rpx;
  height: 80rpx;
  margin: 0 auto 20rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-icon image {
  width: 48rpx;
  height: 48rpx;
}

.shops-nav .nav-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.info-nav .nav-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.transport-nav .nav-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.errand-nav .nav-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.nav-text {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8rpx;
}

.nav-desc {
  display: block;
  font-size: 22rpx;
  color: var(--text-secondary);
}

/* 快速发布区域 */
.quick-publish-section {
  padding: 0 30rpx 30rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.section-more {
  font-size: 24rpx;
  color: var(--text-secondary);
}

.publish-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.publish-item {
  background-color: var(--bg-primary);
  border-radius: var(--border-radius);
  padding: 30rpx 20rpx;
  text-align: center;
  box-shadow: var(--shadow-light);
  transition: transform 0.2s ease;
}

.publish-item:active {
  transform: scale(0.95);
}

.publish-item icon {
  color: var(--primary-color);
  margin-bottom: 12rpx;
}

.publish-item text {
  display: block;
  font-size: 22rpx;
  color: var(--text-primary);
}

/* 信息流区域 */
.feed-section {
  padding: 0 30rpx 30rpx;
}

.feed-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.feed-item {
  background-color: var(--bg-primary);
  border-radius: var(--border-radius);
  padding: 30rpx;
  box-shadow: var(--shadow-light);
}

.feed-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.feed-type {
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
  color: white;
}

.feed-type.shops {
  background-color: #667eea;
}

.feed-type.information {
  background-color: #f5576c;
}

.feed-type.transportation {
  background-color: #4facfe;
}

.feed-type.errands {
  background-color: #43e97b;
}

.feed-time {
  font-size: 22rpx;
  color: var(--text-secondary);
}

.feed-title {
  font-size: 30rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 12rpx;
}

.feed-content {
  font-size: 26rpx;
  color: var(--text-secondary);
  margin-bottom: 20rpx;
  line-height: 1.5;
}

.feed-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.feed-location,
.feed-contact {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 22rpx;
  color: var(--text-secondary);
}

.feed-contact {
  color: var(--primary-color);
}
