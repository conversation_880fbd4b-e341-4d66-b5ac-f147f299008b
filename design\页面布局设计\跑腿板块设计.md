# 跑腿板块设计规范

## 📱 页面概述

跑腿板块专注于本地跑腿服务信息对接，包括代购、代送、代办、帮忙等各类跑腿任务。

## 🎨 整体布局

### 页面结构
```
跑腿页面 (750rpx × 1334rpx)
├── 导航栏 (88rpx)
├── 服务类型筛选栏 (120rpx)
├── 快速发布区域 (100rpx)
├── 跑腿任务列表 (可滚动)
└── Tab栏 (98rpx)
```

### 主题色彩
- **主色调**: 绿青渐变 (#43e97b → #38f9d7)
- **浅色背景**: rgba(67, 233, 123, 0.1)
- **按钮色**: #43e97b
- **选中状态**: #38f9d7

## 🧭 导航栏设计

```css
.nav-bar {
  height: 88rpx;
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32rpx;
  font-weight: 600;
}
```

## 🏃 服务类型筛选

### 筛选栏设计
```css
.service-filter {
  background: white;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #EBEDF0;
}

.filter-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12rpx;
}

.filter-service {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12rpx 8rpx;
  border-radius: 12rpx;
  background: #F8F9FA;
  border: 1rpx solid #EBEDF0;
  transition: all 0.2s ease;
}

.filter-service-active {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  color: white;
  border-color: transparent;
  box-shadow: 0 2rpx 8rpx rgba(67, 233, 123, 0.3);
}

.filter-icon {
  width: 28rpx;
  height: 28rpx;
  margin-bottom: 6rpx;
}

.filter-text {
  font-size: 20rpx;
  font-weight: 500;
  text-align: center;
}
```

### 服务类型选项
- **代购** (购物代买)
- **代送** (物品配送)
- **代办** (事务代办)
- **帮忙** (其他帮助)

## 🚀 快速发布区域

```css
.quick-publish {
  background: rgba(67, 233, 123, 0.05);
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.publish-icon {
  width: 40rpx;
  height: 40rpx;
  color: #43e97b;
}

.publish-text {
  flex: 1;
  font-size: 26rpx;
  color: #191F25;
}

.publish-btn {
  padding: 12rpx 24rpx;
  background: linear-gradient(135deg, #43e97b, #38f9d7);
  color: white;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}
```

## 🎯 跑腿任务卡片

### 基础卡片设计
```css
.errand-card {
  background: white;
  border-radius: 12rpx;
  padding: 24rpx;
  margin: 0 30rpx 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  border-left: 4rpx solid #43e97b;
}

.errand-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.errand-type {
  padding: 6rpx 12rpx;
  border-radius: 6rpx;
  font-size: 20rpx;
  font-weight: 500;
  color: white;
  background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.errand-urgency {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.urgency-high {
  background: #FA5151;
  color: white;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  font-size: 18rpx;
  animation: pulse 2s infinite;
}

.urgency-medium {
  background: #FFB800;
  color: white;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  font-size: 18rpx;
}

.urgency-low {
  background: rgba(67, 233, 123, 0.2);
  color: #43e97b;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  font-size: 18rpx;
}

.errand-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #191F25;
  line-height: 1.4;
  margin-bottom: 12rpx;
}

.errand-description {
  font-size: 24rpx;
  color: #8B9197;
  line-height: 1.5;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
```

## 🛒 代购任务卡片

### 代购特殊元素
```css
.shopping-card {
  border-left-color: #43e97b;
}

.shopping-list {
  background: rgba(67, 233, 123, 0.05);
  border-radius: 8rpx;
  padding: 16rpx;
  margin-bottom: 16rpx;
}

.shopping-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8rpx 0;
  border-bottom: 1rpx solid #EBEDF0;
}

.shopping-item:last-child {
  border-bottom: none;
}

.item-name {
  font-size: 24rpx;
  color: #191F25;
}

.item-quantity {
  font-size: 22rpx;
  color: #8B9197;
}

.shopping-budget {
  display: flex;
  align-items: baseline;
  gap: 6rpx;
  margin-bottom: 16rpx;
}

.budget-label {
  font-size: 22rpx;
  color: #8B9197;
}

.budget-amount {
  font-size: 28rpx;
  font-weight: 600;
  color: #43e97b;
}

.shopping-location {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.location-icon {
  width: 20rpx;
  height: 20rpx;
  color: #43e97b;
}

.location-text {
  font-size: 24rpx;
  color: #191F25;
}
```

## 📦 代送任务卡片

### 代送特殊元素
```css
.delivery-card {
  border-left-color: #38f9d7;
}

.delivery-route {
  background: rgba(67, 233, 123, 0.05);
  border-radius: 8rpx;
  padding: 16rpx;
  margin-bottom: 16rpx;
}

.route-point {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 12rpx;
}

.route-point:last-child {
  margin-bottom: 0;
}

.route-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  flex-shrink: 0;
}

.route-pickup {
  background: #43e97b;
}

.route-delivery {
  background: #38f9d7;
}

.route-address {
  font-size: 24rpx;
  color: #191F25;
}

.route-contact {
  font-size: 22rpx;
  color: #8B9197;
  margin-left: 24rpx;
}

.delivery-items {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.delivery-item {
  padding: 4rpx 8rpx;
  background: rgba(67, 233, 123, 0.1);
  color: #43e97b;
  border-radius: 4rpx;
  font-size: 20rpx;
}

.delivery-requirements {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.requirement-tag {
  padding: 4rpx 8rpx;
  background: rgba(56, 249, 215, 0.1);
  color: #38f9d7;
  border-radius: 4rpx;
  font-size: 20rpx;
}
```

## 📋 代办任务卡片

### 代办特殊元素
```css
.paperwork-card {
  border-left-color: #43e97b;
}

.paperwork-type {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.paperwork-icon {
  width: 24rpx;
  height: 24rpx;
  color: #43e97b;
}

.paperwork-category {
  font-size: 24rpx;
  color: #191F25;
  font-weight: 500;
}

.paperwork-details {
  background: rgba(67, 233, 123, 0.05);
  border-radius: 8rpx;
  padding: 16rpx;
  margin-bottom: 16rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6rpx 0;
  font-size: 24rpx;
}

.detail-label {
  color: #8B9197;
}

.detail-value {
  color: #191F25;
  font-weight: 500;
}

.paperwork-documents {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.document-tag {
  padding: 4rpx 8rpx;
  background: rgba(67, 233, 123, 0.1);
  color: #43e97b;
  border-radius: 4rpx;
  font-size: 20rpx;
}
```

## 🤝 帮忙任务卡片

### 帮忙特殊元素
```css
.help-card {
  border-left-color: #38f9d7;
}

.help-category {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.help-icon {
  width: 24rpx;
  height: 24rpx;
  color: #38f9d7;
}

.help-type {
  font-size: 24rpx;
  color: #191F25;
  font-weight: 500;
}

.help-skills {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.skill-tag {
  padding: 4rpx 8rpx;
  background: rgba(56, 249, 215, 0.1);
  color: #38f9d7;
  border-radius: 4rpx;
  font-size: 20rpx;
}

.help-duration {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.duration-icon {
  width: 20rpx;
  height: 20rpx;
  color: #38f9d7;
}

.duration-text {
  font-size: 24rpx;
  color: #191F25;
}
```

## 💰 报酬信息设计

### 报酬显示
```css
.reward-section {
  background: rgba(67, 233, 123, 0.05);
  border-radius: 8rpx;
  padding: 16rpx;
  margin-bottom: 16rpx;
}

.reward-amount {
  display: flex;
  align-items: baseline;
  gap: 6rpx;
  margin-bottom: 8rpx;
}

.reward-price {
  font-size: 32rpx;
  font-weight: 700;
  color: #43e97b;
}

.reward-unit {
  font-size: 22rpx;
  color: #8B9197;
}

.reward-type {
  font-size: 22rpx;
  color: #8B9197;
}

.reward-negotiable {
  color: #FFB800;
  font-weight: 500;
}

.reward-fixed {
  color: #43e97b;
  font-weight: 500;
}
```

## ⏰ 时间要求设计

### 时间信息
```css
.time-requirements {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.time-start {
  display: flex;
  align-items: center;
  gap: 6rpx;
  font-size: 22rpx;
  color: #8B9197;
}

.time-deadline {
  display: flex;
  align-items: center;
  gap: 6rpx;
  font-size: 22rpx;
  color: #FA5151;
  font-weight: 500;
}

.time-icon {
  width: 16rpx;
  height: 16rpx;
}

.time-flexible {
  color: #43e97b;
}

.time-urgent {
  color: #FA5151;
  animation: pulse 2s infinite;
}
```

## 📱 交互设计

### 卡片交互
```css
.errand-card:active {
  transform: scale(0.98);
  transition: transform 0.1s ease;
}

.contact-btn:active {
  background: linear-gradient(135deg, #3dd470, #32e6c4);
}
```

### 紧急任务动画
```css
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.urgent-task {
  animation: pulse 2s infinite;
}
```

## 🎯 特殊功能

### 任务状态标识
```css
.task-status {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  font-size: 18rpx;
  font-weight: 500;
}

.status-open {
  background: #43e97b;
  color: white;
}

.status-in-progress {
  background: #FFB800;
  color: white;
}

.status-completed {
  background: #8B9197;
  color: white;
}
```

### 信任度标识
```css
.trust-badge {
  display: flex;
  align-items: center;
  gap: 4rpx;
  font-size: 20rpx;
  color: #43e97b;
}

.trust-icon {
  width: 16rpx;
  height: 16rpx;
}

.trust-verified {
  color: #52C41A;
}

.trust-premium {
  color: #FFB800;
}
```

---

**设计要点**:
1. 绿青色主题体现跑腿服务的活力和效率
2. 紧急程度用颜色和动画明确标识
3. 不同跑腿类型有专门的信息展示
4. 报酬和时间要求突出显示，便于快速判断
