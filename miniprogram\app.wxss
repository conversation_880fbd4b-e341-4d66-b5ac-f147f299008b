/* 本地助手小程序 - 全局样式 */

/* 全局变量定义 */
page {
  --primary-color: #07C160;
  --secondary-color: #10AEFF;
  --accent-color: #FF6B35;
  --warning-color: #FFB800;
  --error-color: #FA5151;
  --text-primary: #191F25;
  --text-secondary: #8B9197;
  --text-placeholder: #C5CAD0;
  --bg-primary: #FFFFFF;
  --bg-secondary: #F8F9FA;
  --border-color: #EBEDF0;
  --shadow-light: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
  --border-radius: 12rpx;
  --border-radius-large: 20rpx;
}

/* 重置样式 */
* {
  box-sizing: border-box;
}

page {
  background-color: var(--bg-secondary);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  line-height: 1.6;
}

/* 通用容器 */
.container {
  padding: 0 30rpx;
}

.page-container {
  min-height: 100vh;
  background-color: var(--bg-secondary);
}

/* 卡片样式 */
.card {
  background-color: var(--bg-primary);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  margin-bottom: 20rpx;
  overflow: hidden;
}

.card-header {
  padding: 30rpx;
  border-bottom: 1rpx solid var(--border-color);
}

.card-body {
  padding: 30rpx;
}

.card-footer {
  padding: 20rpx 30rpx;
  background-color: var(--bg-secondary);
  border-top: 1rpx solid var(--border-color);
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 40rpx;
  border-radius: var(--border-radius);
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
  border: none;
  outline: none;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:active {
  background-color: #06AD56;
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: white;
}

.btn-outline {
  background-color: transparent;
  border: 2rpx solid var(--primary-color);
  color: var(--primary-color);
}

.btn-small {
  padding: 12rpx 24rpx;
  font-size: 24rpx;
}

.btn-large {
  padding: 28rpx 60rpx;
  font-size: 32rpx;
}

.btn-block {
  width: 100%;
}

.btn-disabled {
  opacity: 0.6;
  pointer-events: none;
}

/* 文本样式 */
.text-primary {
  color: var(--text-primary);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-placeholder {
  color: var(--text-placeholder);
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-bold {
  font-weight: 600;
}

.text-large {
  font-size: 32rpx;
}

.text-small {
  font-size: 24rpx;
}

.text-xs {
  font-size: 20rpx;
}

/* 布局样式 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  align-items: center;
  justify-content: center;
}

.flex-between {
  justify-content: space-between;
}

.flex-around {
  justify-content: space-around;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-1 {
  flex: 1;
}

/* 间距样式 */
.mt-10 { margin-top: 10rpx; }
.mt-20 { margin-top: 20rpx; }
.mt-30 { margin-top: 30rpx; }
.mb-10 { margin-bottom: 10rpx; }
.mb-20 { margin-bottom: 20rpx; }
.mb-30 { margin-bottom: 30rpx; }
.ml-10 { margin-left: 10rpx; }
.ml-20 { margin-left: 20rpx; }
.mr-10 { margin-right: 10rpx; }
.mr-20 { margin-right: 20rpx; }

.p-10 { padding: 10rpx; }
.p-20 { padding: 20rpx; }
.p-30 { padding: 30rpx; }
.pt-10 { padding-top: 10rpx; }
.pt-20 { padding-top: 20rpx; }
.pb-10 { padding-bottom: 10rpx; }
.pb-20 { padding-bottom: 20rpx; }
.pl-10 { padding-left: 10rpx; }
.pl-20 { padding-left: 20rpx; }
.pr-10 { padding-right: 10rpx; }
.pr-20 { padding-right: 20rpx; }

/* 标签样式 */
.tag {
  display: inline-block;
  padding: 8rpx 16rpx;
  font-size: 20rpx;
  border-radius: 8rpx;
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
}

.tag-primary {
  background-color: var(--primary-color);
  color: white;
}

.tag-success {
  background-color: #52C41A;
  color: white;
}

.tag-warning {
  background-color: var(--warning-color);
  color: white;
}

.tag-error {
  background-color: var(--error-color);
  color: white;
}

/* 分割线 */
.divider {
  height: 1rpx;
  background-color: var(--border-color);
  margin: 20rpx 0;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 30rpx;
  color: var(--text-secondary);
}

.empty-state image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 40rpx;
  color: var(--text-secondary);
}

/* 安全区域适配 */
.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}
