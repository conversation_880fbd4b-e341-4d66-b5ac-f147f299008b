// 本地助手小程序 - 主应用文件
App({
  onLaunch: function () {
    console.log('本地助手小程序启动')
    
    // 初始化云开发
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力')
    } else {
      wx.cloud.init({
        // env 参数说明：
        //   env 参数决定接下来小程序发起的云开发调用（wx.cloud.xxx）会默认请求到哪个云环境的资源
        //   此处请填入环境 ID, 环境 ID 可打开云控制台查看
        //   如不填则使用默认环境（第一个创建的环境）
        env: 'local-helper-dev', // 请替换为您的云开发环境ID
        traceUser: true,
      })
    }

    // 获取用户信息
    this.getUserProfile()
  },

  onShow: function () {
    console.log('小程序显示')
  },

  onHide: function () {
    console.log('小程序隐藏')
  },

  onError: function (msg) {
    console.error('小程序错误:', msg)
  },

  // 获取用户信息
  getUserProfile: function() {
    const that = this
    wx.getSetting({
      success: res => {
        if (res.authSetting['scope.userInfo']) {
          // 已经授权，可以直接调用 getUserInfo 获取头像昵称
          wx.getUserInfo({
            success: res => {
              that.globalData.userInfo = res.userInfo
              // 由于 getUserInfo 是网络请求，可能会在 Page.onLoad 之后才返回
              // 所以此处加入 callback 以防止这种情况
              if (that.userInfoReadyCallback) {
                that.userInfoReadyCallback(res)
              }
            }
          })
        }
      }
    })
  },

  // 全局数据
  globalData: {
    userInfo: null,
    userLocation: null,
    systemInfo: null
  },

  // 工具函数
  utils: {
    // 格式化时间
    formatTime: function(date) {
      const year = date.getFullYear()
      const month = date.getMonth() + 1
      const day = date.getDate()
      const hour = date.getHours()
      const minute = date.getMinutes()
      const second = date.getSeconds()

      return [year, month, day].map(this.formatNumber).join('/') + ' ' + [hour, minute, second].map(this.formatNumber).join(':')
    },

    formatNumber: function(n) {
      n = n.toString()
      return n[1] ? n : '0' + n
    },

    // 计算距离
    getDistance: function(lat1, lng1, lat2, lng2) {
      const radLat1 = lat1 * Math.PI / 180.0
      const radLat2 = lat2 * Math.PI / 180.0
      const a = radLat1 - radLat2
      const b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0
      let s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)))
      s = s * 6378.137
      s = Math.round(s * 10000) / 10000
      return s
    },

    // 显示加载提示
    showLoading: function(title = '加载中...') {
      wx.showLoading({
        title: title,
        mask: true
      })
    },

    // 隐藏加载提示
    hideLoading: function() {
      wx.hideLoading()
    },

    // 显示成功提示
    showSuccess: function(title) {
      wx.showToast({
        title: title,
        icon: 'success',
        duration: 2000
      })
    },

    // 显示错误提示
    showError: function(title) {
      wx.showToast({
        title: title,
        icon: 'none',
        duration: 2000
      })
    }
  }
})
