# 本地助手小程序 - UI设计规范

## 🎨 设计系统概述

本文档定义了"本地助手"微信小程序的完整视觉设计系统，确保产品的一致性和专业性。

## 📐 基础设计规范

### 设计尺寸
- **设计基准**: 750rpx × 1334rpx (iPhone 6/7/8)
- **实际显示**: 375px × 667px
- **状态栏高度**: 44rpx (22px)
- **导航栏高度**: 88rpx (44px)
- **Tab栏高度**: 98rpx (49px)
- **安全区域**: 底部68rpx (34px)

### 栅格系统
- **页面边距**: 30rpx (15px)
- **内容间距**: 20rpx (10px)
- **组件间距**: 16rpx (8px)
- **文字间距**: 12rpx (6px)

## 🎨 色彩系统

### 主色调
```css
/* 品牌主色 */
--primary-color: #07C160;        /* 微信绿 */
--primary-light: #52C41A;        /* 浅绿色 */
--primary-dark: #06AD56;         /* 深绿色 */

/* 辅助色 */
--secondary-color: #10AEFF;      /* 蓝色 */
--accent-color: #FF6B35;         /* 橙色 */
--warning-color: #FFB800;        /* 黄色 */
--error-color: #FA5151;          /* 红色 */
--success-color: #52C41A;        /* 成功绿 */
```

### 文本色彩
```css
/* 文本颜色 */
--text-primary: #191F25;         /* 主要文本 */
--text-secondary: #8B9197;       /* 次要文本 */
--text-placeholder: #C5CAD0;     /* 占位文本 */
--text-disabled: #E5E5E5;        /* 禁用文本 */
--text-white: #FFFFFF;           /* 白色文本 */
```

### 背景色彩
```css
/* 背景颜色 */
--bg-primary: #FFFFFF;           /* 主背景 */
--bg-secondary: #F8F9FA;         /* 次背景 */
--bg-tertiary: #F0F0F0;          /* 三级背景 */
--border-color: #EBEDF0;         /* 边框色 */
--divider-color: #F0F0F0;        /* 分割线 */
```

### 板块专用色彩
```css
/* 商铺板块 - 蓝紫渐变 */
--shops-start: #667eea;
--shops-end: #764ba2;
--shops-light: rgba(102, 126, 234, 0.1);

/* 信息板块 - 粉红渐变 */
--info-start: #f093fb;
--info-end: #f5576c;
--info-light: rgba(240, 147, 251, 0.1);

/* 出行板块 - 蓝青渐变 */
--transport-start: #4facfe;
--transport-end: #00f2fe;
--transport-light: rgba(79, 172, 254, 0.1);

/* 跑腿板块 - 绿青渐变 */
--errands-start: #43e97b;
--errands-end: #38f9d7;
--errands-light: rgba(67, 233, 123, 0.1);
```

## 📝 字体系统

### 字体家族
```css
font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 
             'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 
             'Helvetica Neue', Helvetica, Arial, sans-serif;
```

### 字体规范
```css
/* 标题字体 */
--font-size-h1: 36rpx;           /* 18px - 页面主标题 */
--font-size-h2: 32rpx;           /* 16px - 区块标题 */
--font-size-h3: 28rpx;           /* 14px - 卡片标题 */

/* 正文字体 */
--font-size-body-l: 26rpx;       /* 13px - 大正文 */
--font-size-body-m: 24rpx;       /* 12px - 中正文 */
--font-size-body-s: 22rpx;       /* 11px - 小正文 */

/* 辅助字体 */
--font-size-caption: 20rpx;      /* 10px - 说明文字 */
--font-size-button: 28rpx;       /* 14px - 按钮文字 */

/* 字重 */
--font-weight-light: 300;
--font-weight-normal: 400;
--font-weight-medium: 500;
--font-weight-semibold: 600;
--font-weight-bold: 700;

/* 行高 */
--line-height-tight: 1.2;
--line-height-normal: 1.4;
--line-height-relaxed: 1.6;
```

## 🔲 组件规范

### 圆角系统
```css
--border-radius-xs: 4rpx;        /* 2px - 小标签 */
--border-radius-sm: 8rpx;        /* 4px - 按钮 */
--border-radius-md: 12rpx;       /* 6px - 输入框 */
--border-radius-lg: 16rpx;       /* 8px - 卡片 */
--border-radius-xl: 20rpx;       /* 10px - 大卡片 */
--border-radius-2xl: 24rpx;      /* 12px - 特殊卡片 */
--border-radius-full: 50%;       /* 圆形 */
```

### 阴影系统
```css
/* 阴影效果 */
--shadow-xs: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
--shadow-sm: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
--shadow-md: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
--shadow-lg: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
--shadow-xl: 0 12rpx 48rpx rgba(0, 0, 0, 0.25);
```

### 按钮规范
```css
/* 按钮尺寸 */
--button-height-sm: 64rpx;       /* 32px */
--button-height-md: 80rpx;       /* 40px */
--button-height-lg: 96rpx;       /* 48px */

/* 按钮内边距 */
--button-padding-sm: 24rpx 32rpx;
--button-padding-md: 28rpx 40rpx;
--button-padding-lg: 32rpx 48rpx;

/* 最小点击区域 */
--min-touch-size: 88rpx;         /* 44px */
```

## 📱 组件设计规范

### 1. 卡片组件 (Card)
```css
.card {
  background: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.card-header {
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid var(--border-color);
  margin-bottom: 20rpx;
}

.card-title {
  font-size: var(--font-size-h3);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}
```

### 2. 按钮组件 (Button)
```css
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-button);
  font-weight: var(--font-weight-medium);
  transition: all 0.2s ease;
  border: none;
  outline: none;
}

.btn-primary {
  background: var(--primary-color);
  color: var(--text-white);
  height: var(--button-height-md);
  padding: var(--button-padding-md);
}

.btn-secondary {
  background: var(--secondary-color);
  color: var(--text-white);
}

.btn-outline {
  background: transparent;
  border: 2rpx solid var(--primary-color);
  color: var(--primary-color);
}
```

### 3. 标签组件 (Tag)
```css
.tag {
  display: inline-block;
  padding: 8rpx 16rpx;
  border-radius: var(--border-radius-xs);
  font-size: var(--font-size-caption);
  font-weight: var(--font-weight-medium);
  background: var(--bg-secondary);
  color: var(--text-secondary);
}

.tag-primary {
  background: var(--primary-color);
  color: var(--text-white);
}

.tag-shops {
  background: linear-gradient(135deg, var(--shops-start), var(--shops-end));
  color: var(--text-white);
}
```

### 4. 输入框组件 (Input)
```css
.input {
  width: 100%;
  height: var(--button-height-md);
  padding: 0 24rpx;
  border: 2rpx solid var(--border-color);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-body-l);
  background: var(--bg-primary);
  color: var(--text-primary);
}

.input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4rpx rgba(7, 193, 96, 0.1);
}

.input::placeholder {
  color: var(--text-placeholder);
}
```

## 🎯 板块专用设计

### 商铺板块设计
- **主色调**: 蓝紫渐变 (#667eea → #764ba2)
- **图标风格**: 商店、购物袋相关图标
- **卡片特色**: 商家头像 + 认证标识
- **按钮样式**: 蓝紫色渐变背景

### 信息板块设计
- **主色调**: 粉红渐变 (#f093fb → #f5576c)
- **图标风格**: 信息、公告相关图标
- **卡片特色**: 分类标签 + 时间戳
- **按钮样式**: 粉红色渐变背景

### 出行板块设计
- **主色调**: 蓝青渐变 (#4facfe → #00f2fe)
- **图标风格**: 交通、路线相关图标
- **卡片特色**: 起点终点 + 时间显示
- **按钮样式**: 蓝青色渐变背景

### 跑腿板块设计
- **主色调**: 绿青渐变 (#43e97b → #38f9d7)
- **图标风格**: 服务、帮助相关图标
- **卡片特色**: 任务类型 + 紧急程度
- **按钮样式**: 绿青色渐变背景

## 📏 响应式设计

### 屏幕适配
```css
/* 小屏设备 (iPhone SE) */
@media (max-width: 320px) {
  --page-padding: 20rpx;
  --font-size-h1: 32rpx;
}

/* 大屏设备 (iPhone Plus) */
@media (min-width: 414px) {
  --page-padding: 40rpx;
  --font-size-h1: 40rpx;
}
```

### 安全区域适配
```css
.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}
```

---

**设计原则**: 简洁、一致、易用、美观
**更新日期**: 2024年12月
**版本**: v1.0
